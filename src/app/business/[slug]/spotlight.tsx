"use client";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useEffect, useRef } from "react";

interface AnnouncementItem {
  id: number;
  title: string;
  sport: string;
  date: string;
}

interface SpotLightsProps {
  data: AnnouncementItem[] | null; // allow `null` while loading
}

const SpotLights = ({ data }: SpotLightsProps) => {
  const carouselRef = useRef<any>(null);

  const formatToDayMonthYear = (isoDate: string): string => {
    const date = new Date(isoDate);
    const options: Intl.DateTimeFormatOptions = {
      day: "2-digit",
      month: "short",
      year: "numeric",
    };
    return date.toLocaleDateString("en-US", options);
  };

  useEffect(() => {
    if (!data || data.length === 0) return;
    const interval = setInterval(() => {
      if (carouselRef.current) {
        carouselRef.current.scrollTo(
          (carouselRef.current.selectedScrollSnap() + 1) % data.length
        );
      }
    }, 2000);
    return () => clearInterval(interval);
  }, [data]);

  // Show nothing if no data at all (including loading)
  if (data === null) {
    return (
      <div className="bg-slate-100 flex flex-col p-4 rounded-md w-full max-w-xs mx-auto">
        <h2 className="font-bold text-xl mx-auto pb-2">Spotlight</h2>
        {/* Skeleton placeholder */}
        <div className="space-y-4 animate-pulse">
          {[...Array(3)].map((_, idx) => (
            <div
              key={idx}
              className="h-[120px] bg-slate-300 rounded-md"
            ></div>
          ))}
        </div>
      </div>
    );
  }

  // Hide component if data is loaded but empty
  if (Array.isArray(data) && data.length === 0) {
    return null;
  }

  return (
    <div className="bg-slate-100 flex flex-col p-4 rounded-md">
      <h2 className="font-bold text-xl mx-auto pb-2">Spotlight</h2>

      <Carousel
        opts={{
          align: "start",
          loop: true,
        }}
        setApi={(api) => (carouselRef.current = api)}
        orientation="vertical"
        className="w-full max-w-xs mx-auto h-full"
      >
        <CarouselContent className="h-[450px] w-full gap-1">
          {data?.map((item: any) => {
            console.log("itemitem", item);
            return (
            <CarouselItem key={item.id} className="basis-1/3">
              <Card>
                <CardHeader>
                  <CardTitle className="font-bold">{item.title}</CardTitle>
                </CardHeader>
                <CardContent className="flex justify-between flex-wrap gap-3">
                    <span className="font-semibold text-md">
                      {item?.type?.typeName}
                    </span>
                  <span className="text-secondary text-md font-semibold">
                      {item?.category?.categoryName}
                    </span>

                    <div className="gap-x-5">
                      <span className="text-secondary text-md font-semibold">
                        {formatToDayMonthYear(item?.startDate)}
                      </span>
                      <span className="text-secondary text-md font-semibold">
                        {" - " + formatToDayMonthYear(item?.endDate)}
                  </span>
                    </div>
                </CardContent>
              </Card>
            </CarouselItem>
            );
          })}
        </CarouselContent>
      </Carousel>
    </div>
  );
};

export default SpotLights;
