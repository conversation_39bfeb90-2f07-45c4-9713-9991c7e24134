import ConditionalLayout from "@/components/conditionalLayout";
import type { Metada<PERSON> } from "next";
import { Ralew<PERSON> } from "next/font/google";
import 'swiper/css';
import "./globals.css";
import { Providers } from "./providers";

const raleway = Raleway({
  subsets: ["latin"],
  variable: "--font-raleway",
});

export const metadata: Metadata = {
  title: "Connect Athlete",
  description: "",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={raleway.variable}>
      <body className="font-sans antialiased bg-gray-50 flex flex-col min-h-screen">
        <Providers>
          <ConditionalLayout />
          <main className="flex-grow relative z-10 p-4">
            {children}
          </main>
        </Providers>
      </body>
    </html>
  );
}
