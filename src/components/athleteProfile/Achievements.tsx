import Chip from "@/components/common/Chip"
import CommonCalender from "@/components/common/CommonCalender"
import UploadFiles from "@/components/common/UploadFiles"
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList
} from "@/components/ui/command"
import { RootState } from "@/store"
import { handleUpdateUserInput } from "@/store/slices/athlete/athleteProfileSlice"
import { preventSpaces } from "@/utils/validations"
import { zodResolver } from '@hookform/resolvers/zod'
import { useEffect, useState } from "react"
import { Controller, useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { z } from 'zod'
import AchievementItem from "../common/AchievementItem"
import { Button } from "../ui/button"
import { Input } from "../ui/input"
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover"
import { Switch } from "../ui/switch"
import { Textarea } from "../ui/textarea"


const schema = z
    .object({

        title: z
            .string()
            .min(1, 'Title is required'),
        date: z.date({
            required_error: "Date is required.",
        }),
        blurb: z.string().min(1, 'Description is required'),
        link: z.string().min(1, 'Link is required').url('Invalid url'),
        tags: z
            .array(
                z.object({
                    id: z.string().min(1),
                    name: z.string().min(1),
                })
            )
            .min(1, 'Please select at least one tag')
            .max(3, 'You can select up to 3 tags'),
        file: z
            .instanceof(File)
            .refine((file) => file.size > 0, {
                message: "File is required",
            })
            .refine(
                (file) =>
                    ["application/pdf"].some((type) =>
                        file.type.startsWith(type)
                    ),
                {
                    message: "Only PDF files are allowed",
                }
            ),
    })

const options = [
    { id: 'react', name: 'React' },
    { id: 'next', name: 'Next.js' },
    { id: 'tailwind', name: 'Tailwind' },
    { id: 'redux', name: 'Redux' },
    { id: 'ts', name: 'TypeScript' },
    { id: 'lng', name: 'Long tag name added to check' },
];


const Achievements = () => {
    const { toggleAchievements, achievementData, addedAchievementsList } = useSelector((state: RootState) => state.athleteProfile)
    const dispatch = useDispatch()
    type FormData = z.infer<typeof schema>;

    const handleToggleSection = () => {
        dispatch(handleUpdateUserInput({ name: 'toggleAchievements', value: !toggleAchievements }))
    }

    const {
        control,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<FormData>({
        resolver: zodResolver(schema),
        defaultValues: {
            ...achievementData,
            title: achievementData?.title ?? '',
            link: achievementData?.link ?? '',
            blurb: achievementData?.blurb ?? '',
            tags: achievementData?.tags ?? [],
            file: achievementData?.file ?? undefined,
            date: achievementData?.date ? new Date(achievementData.date) : undefined,
        }
    });

    useEffect(() => {
        reset()
    }, [reset])

    const onSubmit = async (data: FormData) => {
        dispatch(handleUpdateUserInput({ name: 'addedAchievementsList', value: [...addedAchievementsList, data] }))
        reset({})
        console.log(data)
    };

    const onError = (errors: any) => {
        console.error("Form validation errors:", errors);
    };

    return (
        <>
            <div className="flex flex-col items-center gap-5 p-4 rounded-lg bg-slate-100">
                <div className="flex gap-3 items-center">
                    <h3 className="font-bold text-xl">Overall Achievements (Off The Field)</h3>
                    <Switch checked={toggleAchievements} onCheckedChange={handleToggleSection} />
                </div>
                <div className="flex flex-col gap-5">
                    <p className="text-center">Proudest moments beyond sports — academic excellence, leadership awards,
                        community service, and more.</p>
                    {toggleAchievements ? <form onSubmit={handleSubmit(onSubmit, onError)} >
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3  gap-4 ">
                            <div>
                                <Controller
                                    name='date'
                                    control={control}
                                    render={({ field }) => (
                                        <CommonCalender
                                            placeholder={`Date`}
                                            mode='single'
                                            dateValue={field.value}
                                            setDateFn={(date) => field.onChange(date)}
                                        />
                                    )}
                                />
                                {errors.date && (
                                    <p className='text-red-500 text-sm mt-1'>
                                        {errors.date.message}
                                    </p>
                                )}
                            </div>

                            <div>
                                <Controller
                                    name='title'
                                    control={control}
                                    render={({ field }) => (
                                        <Input
                                            {...field}
                                            type={'text'}
                                            placeholder='Enter Title'
                                            onChange={(e) => {
                                                const sanitizedValue = e.target.value
                                                    ?.trimStart()
                                                    ?.replace(preventSpaces, '');
                                                field.onChange(sanitizedValue);
                                            }}
                                            className='mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50'
                                        />
                                    )}
                                />
                                {errors.title && (
                                    <p className='text-red-500 text-sm mt-1'>
                                        {errors.title.message}
                                    </p>
                                )}
                            </div>

                            <div>
                                <Controller
                                    name='link'
                                    control={control}
                                    render={({ field }) => (
                                        <Input
                                            {...field}
                                            type={'text'}
                                            placeholder='Enter Link'
                                            onChange={(e) => {
                                                const sanitizedValue = e.target.value
                                                    ?.trimStart()
                                                    ?.replace(preventSpaces, '');
                                                field.onChange(sanitizedValue);
                                            }}
                                            className='mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50'
                                        />
                                    )}
                                />
                                {errors.link && (
                                    <p className='text-red-500 text-sm mt-1'>
                                        {errors.link.message}
                                    </p>
                                )}
                            </div>

                            <div className="col-span-full">
                                <Controller
                                    name='blurb'
                                    control={control}
                                    render={({ field }) => (
                                        <Textarea
                                            {...field}
                                            placeholder='Enter Description'
                                            onChange={(e) => {
                                                const sanitizedValue = e.target.value
                                                    ?.trimStart()
                                                    ?.replace(preventSpaces, '');
                                                field.onChange(sanitizedValue);
                                            }}
                                            className='bg-[#f5faff] mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50'
                                        />
                                    )}
                                />
                                <div className="flex items-center justify-between">
                                    {errors.blurb && (
                                        <p className='text-red-500 text-sm mt-1'>
                                            {errors.blurb.message}
                                        </p>
                                    )}
                                    {/* <p className="text-right text-destructive self-end text-xs">Max 200 chars</p> */}
                                </div>
                            </div>

                            <div className="col-span-full">
                                <Controller
                                    name="tags"
                                    control={control}
                                    render={({ field }) => {
                                        const selectedTags = field.value || [];
                                        const [open, setOpen] = useState(false);

                                        const handleSelect = (id: string) => {
                                            const tag = options.find((o) => o.id === id);
                                            if (
                                                tag &&
                                                !selectedTags.some((t: { id: string }) => t.id === id) &&
                                                selectedTags.length < 3
                                            ) {
                                                field.onChange([...selectedTags, tag]);
                                                setOpen(false); // 👈 CLOSE popover
                                            }
                                        };

                                        const handleRemove = (id: string) => {
                                            const updated = selectedTags.filter((tag: { id: string }) => tag.id !== id);
                                            field.onChange(updated);
                                        };

                                        const filteredOptions = options.filter(
                                            (option) => !selectedTags.some((tag: { id: string }) => tag.id === option.id)
                                        );

                                        return (
                                            <div className="col-span-full">
                                                <Popover open={open} onOpenChange={setOpen}>
                                                    <PopoverTrigger asChild>
                                                        <Button
                                                            variant="outline"
                                                            role="combobox"
                                                            className="w-full justify-between bg-[#f5faff]"
                                                        >
                                                            Select upto 3 tags...
                                                        </Button>
                                                    </PopoverTrigger>
                                                    <PopoverContent align="start" className="w-full p-0">
                                                        <Command>
                                                            <CommandInput placeholder="Search tags..." />
                                                            <CommandList>
                                                                <CommandEmpty>No tags found.</CommandEmpty>
                                                                <CommandGroup>
                                                                    {filteredOptions.map((option) => (
                                                                        <CommandItem
                                                                            key={option.id}
                                                                            value={option.name}
                                                                            onSelect={() => handleSelect(option.id)}
                                                                        >
                                                                            {option.name}
                                                                        </CommandItem>
                                                                    ))}
                                                                </CommandGroup>
                                                            </CommandList>
                                                        </Command>
                                                    </PopoverContent>
                                                </Popover>

                                                <div className="flex flex-wrap gap-2 mt-2">
                                                    {selectedTags.map((tag: { id: string; name: string }) => (
                                                        <Chip
                                                            key={tag.id}
                                                            id={tag.id}
                                                            label={tag.name}
                                                            onRemove={() => handleRemove(tag.id)}
                                                        />
                                                    ))}
                                                </div>

                                                {errors.tags && (
                                                    <p className="text-red-500 text-sm mt-1">{errors.tags.message}</p>
                                                )}
                                            </div>
                                        );
                                    }}
                                />

                            </div>

                            <div className="col-span-1">
                                {/* <UploadFiles
                                        htmlFor="file"
                                        acceptType={['image/*', 'video/*', 'application/pdf']}
                                        value={null}
                                        onFileSelect={() => { }}
                                        handleRemove={() => { }}
                                    /> */}
                                <Controller
                                    name="file"
                                    control={control}
                                    render={({ field: { value, onChange } }) => (
                                        <UploadFiles
                                            acceptType={["application/pdf"]}
                                            value={value}
                                            onFileSelect={onChange}
                                            handleRemove={() => onChange(null)}
                                            className="w-56"
                                        />
                                    )}
                                />
                            </div>
                        </div>

                        <div className='flex flex-row justify-end mt-4 gap-5'>
                            <Button variant={'outline'} className='w-28 border-primary text-primary hover:text-primary' type='button' onClick={() => reset()}>
                                Cancel
                            </Button>
                            <Button className='w-28' type='submit'>
                                Save
                            </Button>
                        </div>
                    </form> : null}

                    {addedAchievementsList?.length ? addedAchievementsList?.map(each => (
                        <AchievementItem key={each?.title + 'achvm'} item={each} />
                    )) : null}
                </div>
            </div>
        </>
    )
}
export default Achievements