'use client'
import { RootState } from "@/store"
import { handleUpdateUserInput } from "@/store/slices/athlete/athleteProfileSlice"
import { AddedStateLocationsItem, Option } from "@/utils/interfaces"
import { useDispatch, useSelector } from "react-redux"
import AddsContent from "../common/AddsContent"
import PreferedLocations from "../common/PreferedLocations"
import ProfileUrl from "../common/ProfileUrl"
import QuickLinks from "../common/QuickLinks"
import SocialMedia from "../common/SocialMedia"
import SpotLights from "../common/spotlight/SpotLights"
import AboutAthlete from "./AboutAthlete"
import Achievements from "./Achievements"
import Gallery from "./Gallery"
import GrowthDevelopment from "./GrowthDevelopment"
import LearningHighLights from "./LearningHighLights"
import MySports from "./MySports"
import ParentGuardianDetails from "./ParentGuardian"
import PhysicalStats from "./PhysicalStats"
import ProfileCard from "./ProfileCard"

const AthleteProfile = () => {
    const { athleteSocialMediaList, socialMediaToggle, athleteQuickLinksList,
        quickLinksToggle, openVirtualSession,
        selectedState, selectedLocations, athleteAddedStateLocationsList
    } = useSelector((state: RootState) => state.athleteProfile)
    const dispatch = useDispatch()

    const handleChange = (name: string, checked: boolean) => {
        dispatch(handleUpdateUserInput({ name, value: checked }))
    }

    const handleStateLocations = (name: string, value: Option | Option[]) => {
        if (name === 'selectedState') {
            dispatch(handleUpdateUserInput({ name, value }))
            dispatch(handleUpdateUserInput({ name: 'selectedLocations', value: [] }))
        } else if (name === 'selectedLocations') {
            dispatch(handleUpdateUserInput({ name, value }))
        }
    }

    const handleUpdateAddedStateLocations = (updatedList: AddedStateLocationsItem[]) => {
        dispatch(handleUpdateUserInput({ name: 'athleteAddedStateLocationsList', value: [...updatedList] }))
    }


    return (
        <>
            <div className="grid grid-cols-1 md:grid-cols-4 flex-grow w-full">
                <div className="hidden md:block md:col-span-1" />

                <div className="flex flex-col gap-11 py-8 col-span-1 md:col-span-2">
                    <AddsContent />
                    <ProfileUrl />
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                        <div className="lg:col-span-2">
                            <ProfileCard />
                        </div>
                        <SpotLights />
                    </div>
                    <div>
                        <AboutAthlete />
                    </div>
                    <div className="grid grid-cols-1 xl:grid-cols-5 gap-10">
                        <div className="flex flex-col gap-6 xl:col-span-3">
                            <SocialMedia
                                list={athleteSocialMediaList}
                                toggleSocialMediaSection={socialMediaToggle}
                                onChangeToggleSection={(checked) => handleChange('socialMediaToggle', checked)}
                            />
                            <QuickLinks
                                list={athleteQuickLinksList}
                                toggleQuickLinkSection={quickLinksToggle}
                                onChangeToggleSection={(checked) => handleChange('quickLinksToggle', checked)}
                            />
                        </div>
                        <div className="xl:col-span-2">
                            <Gallery />
                        </div>
                    </div>
                    <PhysicalStats />
                    <MySports />
                    <PreferedLocations
                        roleId={1}
                        openVirtualSession={openVirtualSession}
                        handleToggleSession={(checked) => handleChange('openVirtualSession', checked)}
                        selectedState={selectedState}
                        selectedLocations={selectedLocations}
                        handleStateLocations={handleStateLocations}
                        addedStateLocationsList={athleteAddedStateLocationsList}
                        handleUpdateAddedStateLocations={handleUpdateAddedStateLocations}
                    />
                    <LearningHighLights />
                    <GrowthDevelopment />
                    <Achievements />
                    <ParentGuardianDetails />
                </div >

                <div className="hidden md:block md:col-span-1" />
            </div>

        </>
    )
}
export default AthleteProfile