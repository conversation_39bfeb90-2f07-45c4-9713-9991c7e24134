import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { EachSearchItem } from "@/utils/interfaces"
import { PencilLine } from "lucide-react"
import Chip from "../common/Chip"
import SearchInput from "../common/SearchInput"
import { Input } from "../ui/input"
import { Separator } from "../ui/separator"
import { Textarea } from "../ui/textarea"

const LearningHighLights = () => {

    const handleSearchChange = (selected: EachSearchItem | null, name: string) => {
        console.log(`Selected from ${name}:`, selected)
    }

    return (
        <>
            <div className="flex flex-col items-center gap-5 bg-slate-100 p-4 rounded-lg">
                <h3 className="font-bold text-xl text-wrap">Learning Highlights</h3>
                <div className="self-end">
                    <Button size={'icon'} variant={'outline'}>
                        <PencilLine />
                    </Button>
                </div>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-5 w-full px-5">
                    <div className="flex flex-col gap-1">
                        <Label className="font-semibold">Current School Name</Label>
                        <SearchInput
                            list={[{ value: 1, label: 'First School' }, { value: 2, label: 'School Not Found' }]}
                            name="currentSchoolName"
                            placeholder="Select..."
                            onChange={handleSearchChange}
                        />
                    </div>
                    <div className="flex flex-col gap-1">
                        <Label className="font-semibold">Grad Year</Label>
                        <SearchInput
                            list={[{ value: 1, label: 'General' }]}
                            name="gradYear"
                            placeholder="Select..."
                            onChange={handleSearchChange}
                        />
                    </div>
                    <div className="flex flex-col gap-1">
                        <Label className="font-semibold">Current Year in School</Label>
                        <SearchInput
                            list={[{ value: 1, label: 'General' }]}
                            name="currentYearInSchool"
                            placeholder="Select..."
                            onChange={handleSearchChange}
                        />
                    </div>
                    <div className="flex flex-col gap-1">
                        <Label className="font-semibold">Overall Academic Standing</Label>
                        <SearchInput
                            list={[{ value: 1, label: 'General' }]}
                            name="overAllAcademicStanding"
                            placeholder="Select..."
                            onChange={handleSearchChange}
                        />
                    </div>
                    <div className="flex flex-col gap-1">
                        <Label className="font-semibold">GPA</Label>
                        <Input placeholder="GPA" />
                    </div>
                    <div className="flex flex-col gap-1">
                        <Label className="font-semibold">SAT</Label>
                        <Input placeholder="SAT" />
                    </div>
                    <div className="flex flex-col gap-1">
                        <Label className="font-semibold">ACT</Label>
                        <Input placeholder="ACT" />
                    </div>
                </div>
                <div className="flex flex-col gap-1 mt-5 px-3  w-full">
                    <Label className="font-semibold text-wrap">Want to share more about your progress? (Optional)</Label>
                    <Textarea className="border-slate-300 bg-[#f5faff] " />
                    <span className="text-destructive text-xs text-end font-semibold">200 Char Max</span>
                </div>
                <Separator />
                <div className="flex flex-col items-center gap-4 px-3  w-full">
                    <div className="flex flex-col items-center justify-center gap-1">
                        <h5 className="font-bold text-lg">Learning Strength Area</h5>
                        <span className="text-sm font-semibold text-center text-wrap">(Topics Athlete Enjoys - Choose Any 2)</span>
                    </div>
                    <div className="flex flex-col md:flex-row items-center gap-5 w-full">
                        <SearchInput
                            list={[{ value: 1, label: 'Maths' }, { value: 2, label: 'Science' }]}
                            name=''
                            placeholder="Select..."
                            onChange={handleSearchChange}
                        />
                        <Button>Add</Button>
                    </div>
                    {/* List of Subjcets */}
                    <Chip label={'Math'} id={2} onRemove={() => { }} />
                </div>
                <Separator />
                <div className="flex flex-col gap-4 px-3 w-full">
                    <h5 className="font-bold text-lg text-center">Proud Academic Moment</h5>
                    <div className="flex flex-col">
                        <Textarea className="border-slate-300 w-full bg-[#f5faff] " />
                        <span className="text-destructive text-xs self-end font-semibold">400 Char Max</span>
                    </div>
                </div>
            </div >
        </>
    )
}
export default LearningHighLights