import { PencilLine } from "lucide-react"
import { But<PERSON> } from "../ui/button"
import { Input } from "../ui/input"
import { Label } from "../ui/label"

const ParentGuardianDetails = () => {
    return (
        <>
            <div className="flex flex-col gap-3 bg-slate-100 p-4 rounded-lg px-5">
                <h3 className="text-xl font-bold text-center">Primary Parent/Legal Guardian Contact Details</h3>
                <Button size={'icon'} variant={'outline'} className="self-end">
                    <PencilLine />
                </Button>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                    <div className="flex flex-col gap-1">
                        <Label>First Name</Label>
                        <Input className="border-slate-300" />
                    </div>
                    <div className="flex flex-col gap-1">
                        <Label>Last Name</Label>
                        <Input className="border-slate-300" />
                    </div>
                    <div className="flex flex-col gap-1">
                        <Label>Contact Phone</Label>
                        <Input className="border-slate-300" />
                    </div>
                    <div className="flex flex-col gap-1">
                        <Label>Contact Email</Label>
                        <Input className="border-slate-300" />
                    </div>
                    <div className="flex flex-col gap-1">
                        <Label>Relationship to Athlete</Label>
                        <Input className="border-slate-300" />
                    </div>
                    <div className="flex flex-col gap-1">
                        <Label>Last T&C Accepted</Label>
                        <Input className="border-slate-300" />
                    </div>
                </div>
            </div>
        </>
    )
}
export default ParentGuardianDetails