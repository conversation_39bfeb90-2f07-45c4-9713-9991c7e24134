import { EachSearchItem } from "@/utils/interfaces"
import { X } from "lucide-react"
import SearchInput from "../common/SearchInput"
import { Button } from "../ui/button"
import { Label } from "../ui/label"
import { Textarea } from "../ui/textarea"

const GrowthDevelopment = () => {

    const handleSearchChange = (selected: EachSearchItem | null, name: string) => {
        console.log(`Selected from ${name}:`, selected)
    }

    return (
        <>
            <div className="bg-slate-100 rounded-lg p-4 flex flex-col items-center gap-4 px-3 w-full">
                <h5 className="font-bold text-lg text-center">Growth & Development Interests</h5>
                <div className="flex flex-col items-center justify-center gap-1">
                </div>
                <div className="flex flex-col md:flex-row items-center gap-5 w-full">
                    <SearchInput
                        list={[{ value: 1, label: 'Recreational' }, { value: 2, label: 'Growth' }]}
                        name=''
                        placeholder="Search..."
                        onChange={handleSearchChange}
                    />
                    <Button>Add</Button>
                </div>
                {/* List of Interests */}
                <div className="bg-secondary text-white rounded-3xl flex items-center font-bold gap-0 p-1 px-3 pr-1">
                    <span className="text-sm">Recreational</span>
                    <X className="cursor-pointer h-4" />
                </div>

                <div className="flex flex-col w-full">
                    <Label className="font-bold my-1">What else would you like to achieve or accomplish?</Label>
                    <Textarea className="border-slate-300 w-full bg-[#f5faff] " />
                    <span className="text-destructive text-xs self-end font-semibold">200 Char Max</span>
                </div>

            </div>

        </>
    )
}
export default GrowthDevelopment