"use client"
import { Pencil<PERSON><PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "../ui/button"

const AboutAthlete = () => {
    const aboutAthlete = "Unlock your full potential by embracing learning every day. Small consistent steps build great habits. Stay curious, stay driven, and remember—growth starts with a single action.Unlock your full potential by embracing learning every day. Small consistent steps build great habits. Stay curious, stay driven, and remember—growth starts with a single action."

    return (
        <div className="flex flex-col justify-center gap-4">
            <h3 className="font-bold text-center text-xl">About <PERSON></h3>
            <div className="flex flex-col bg-slate-100 p-4 rounded-lg overflow-hidden">
                <div className="self-end">
                    <Button variant={'outline'} size={'icon'}>
                        <PencilLine className="h-14 w-11" />
                    </Button>
                </div>
                <p className="text-gray-800">
                    {aboutAthlete}
                </p>
            </div>
        </div>
    )
}
export default AboutAthlete