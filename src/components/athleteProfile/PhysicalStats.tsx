"use client"
import { RootState } from "@/store"
import { handleUpdateUserInput } from "@/store/slices/athlete/athleteProfileSlice"
import { ChangeEvent } from "react"
import { useDispatch, useSelector } from "react-redux"
import { Input } from "../ui/input"
import { Switch } from "../ui/switch"

const PhysicalStats = () => {
    const { physicalStatsToggle, height } = useSelector((state: RootState) => state.athleteProfile)
    const dispatch = useDispatch()
    const handleSocialMediaToggle = (checked: boolean) => {
        dispatch(handleUpdateUserInput({ name: "physicalStatsToggle", value: checked }))
    }

    const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
        const { name, value } = event.target
        dispatch(handleUpdateUserInput({ name, value: value }))
    }

    return (
        <>
            <div className="flex flex-col gap-4 bg-slate-100 p-4 rounded-lg">
                <div className="flex items-center justify-center gap-4">
                    <h3 className="font-bold text-xl text-center">Physical Stats</h3>
                    <Switch name='physicalStatsToggle' checked={physicalStatsToggle} onCheckedChange={handleSocialMediaToggle} />
                </div>
                {physicalStatsToggle ? <div className="bg-slate-200 p-3 gap-8  rounded-md flex justify-center items-center flex-wrap">
                    <p className="text-center">6 Ft 6 Inches</p>
                    <Input className="border-slate-300 w-auto bg-[#f5faff] " value={height} name='height' onChange={handleChange} />
                </div> : null}
            </div>
        </>
    )
}
export default PhysicalStats