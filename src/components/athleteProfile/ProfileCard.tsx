"use client"

import { EachSearchItem } from "@/utils/interfaces";
import { PencilLine } from "lucide-react";
import { useState } from "react";
import SearchInput from "../common/SearchInput";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Separator } from "../ui/separator";

const ProfileCard = () => {
    const [showFull, setShowFull] = useState(false);

    const blurbContent = "Unlock your full potential by embracing learning every day. Small consistent steps build great habits. Stay curious, stay driven, and remember—growth starts with a single action."
    const isLong = blurbContent?.length > 100;
    const preview = blurbContent?.slice(0, 100);

    const handleSearchChange = (selected: EachSearchItem | null, name: string) => {
        console.log(`Selected from ${name}:`, selected)
    }


    return <div className=" bg-slate-100 rounded-lg p-5 flex flex-col gap-5 ">
        <div className="grid grid-cols-1 lg:grid-cols-3 justify-center items-center gap-3">
            <div className="col-span-1">
                <p className="text-md font-bold text-center lg:text-start text-secondary">Premium</p>
                <img src={'/user.svg'} alt='Profile' className="h-36 mx-auto lg:mx-0 text-primary fill-current" />
            </div>

            <div className="col-span-2 flex flex-col gap-6">
                <div className="flex flex-col md:flex-row justify-center md:justify-between items-center gap-3">
                    <div className="flex items-center flex-wrap gap-3">
                        <p className="font-semibold text-wrap">John</p>
                        <p className="font-semibold text-wrap">Deo</p>
                    </div>
                    <Button variant={'outline'} size={'icon'} className="text-center">
                        <PencilLine className="h-14" />
                    </Button>
                </div>

                <div className="flex flex-col bg-slate-200 p-4 rounded-lg overflow-hidden">
                    <div className="text-gray-800">
                        {isLong && !showFull ? `${preview}...` : blurbContent}
                    </div>

                    {isLong && (
                        <button
                            onClick={() => setShowFull(!showFull)}
                            className="text-blue-500 mt-1 self-start hover:underline text-sm"
                        >
                            {showFull ? "Show less" : "Read more..."}
                        </button>
                    )}
                </div>

                <div className="flex justify-between gap-5 flex-wrap">
                    <p>
                        <span className="font-semibold">
                            Age Group: {" "}
                        </span>
                        18+
                    </p>
                    <p>
                        <span className="font-semibold">
                            Gender: {" "}
                        </span>
                        Female
                    </p>
                </div>
            </div>
        </div >
        <Separator />
        <div className="flex justify-center items-center text-wrap flex-wrap">
            <div className="text-secondary font-semibold text-center flex items-center gap-3 justify-center flex-wrap">
                <p className=" text-primary">Primary sport:{" "}</p>
                <p>Football | Basketball | Baseball</p>
            </div>
        </div>
        <div className="grid grid-cols-1 mt-3 gap-4 items-center">
            <div className="flex flex-col gap-1">
                <Label className=" text-primary">Class of Grade Year:{" "}</Label>
                <SearchInput
                    list={[{ value: 1, label: '2024' }, { value: 2, label: '2025' }]}
                    placeholder="Select Grade Year"
                    name="gradeYear"
                    className=""
                    onChange={handleSearchChange}
                />
            </div>
            <div className="flex flex-col gap-1">
                <Label>Current School Name</Label>
                <Input className="border-slate-300" placeholder="School Name" />
            </div>
        </div>
    </div>
}
export default ProfileCard