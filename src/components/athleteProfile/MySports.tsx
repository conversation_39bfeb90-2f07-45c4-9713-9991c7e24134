'use client'
import { But<PERSON> } from "@/components/ui/button"
import { RootState } from "@/store"
import { generateProfileUrl } from "@/utils/routeConfig"
import { Plus } from "lucide-react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useSelector } from "react-redux"
import SportItem from "../common/SportItem"

const MySports = () => {
    const { mySportsList } = useSelector((state: RootState) => state.athleteProfile)
    const { data: session } = useSession()
    const profileUrl = session?.user && generateProfileUrl(session?.user)
    const router = useRouter()

    const handleAddSport = () => {
        router.push(`${profileUrl}/addSport`)
    }

    return (
        <div className="flex flex-col gap-4 bg-slate-100 p-4 rounded-lg">
            <div className="flex flex-col items-center justify-center gap-4">
                <div className="flex flex-col items-center justify-center">
                    <h3 className="font-bold text-xl text-center">My Sports</h3>
                    <span className="text-center">(We recommend not more than 3 primary sports)</span>
                </div>
                <div className="self-end">
                    <Button onClick={handleAddSport} variant="outline" className="gap-2 border-slate-300 font-semibold hover:text-secondary">
                        <Plus />
                        Add Sport
                    </Button>
                </div>
            </div>

            <div className="flex flex-col gap-5">
                {mySportsList?.map(each => <SportItem key={each.id} item={each} />)}
            </div>
        </div>
    )
}
export default MySports