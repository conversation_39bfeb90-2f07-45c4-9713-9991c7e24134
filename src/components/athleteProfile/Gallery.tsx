"use client"
import ImageUpload from "@/components/common/ImagesUpload"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { RootState } from "@/store"
import { handleUpdateUserInput } from "@/store/slices/athlete/athleteProfileSlice"
import { PencilLine } from "lucide-react"
import { useDispatch, useSelector } from "react-redux"

const Gallery = () => {
    const { athleteGalleryList, galleryToggle } = useSelector((state: RootState) => state.athleteProfile)
    const dispatch = useDispatch()

    const handleQuickLinkToggle = (checked: boolean) => {
        dispatch(handleUpdateUserInput({ name: "galleryToggle", value: checked }))
    }

    return (
        <div className="w-full h-full flex flex-col gap-3 bg-slate-100 p-4 rounded-lg">
            <div className="flex items-center justify-center gap-4">
                <h3 className="font-bold text-xl text-center">Gallery</h3>
                <Switch name='galleryToggle' checked={galleryToggle} onCheckedChange={handleQuickLinkToggle} />
            </div>
            {galleryToggle ?
                <div className="flex flex-col gap-4 ">
                    {athleteGalleryList?.map(each => (
                        <div className="flex flex-col " key={each?.id}>
                            <div className="self-center">
                                <ImageUpload
                                    value={[]}
                                    onChange={() => { }}
                                    maxImages={1}
                                    maxSize={1024}
                                    name={each?.id}
                                    width="max-w-38"
                                />
                            </div>
                            <div className="bg-slate-200 w-full rounded-lg p-1 px-2 flex justify-between items-center gap-4">
                                {each?.isEditable ?
                                    <Input placeholder="Image Title" value={each?.imageTitle} className="w-full border-slate-300" />
                                    :
                                    <p>{each?.imageTitle}</p>}
                                <Button variant={'outline'} size={'icon'}>
                                    <PencilLine />
                                </Button>
                            </div>
                        </div>
                    ))}
                </div> : null}
        </div>
    )
}
export default Gallery