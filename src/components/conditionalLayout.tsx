'use client'
import { useRoleBasedRoute } from "@/hooks/useRoleBasedRouting"
import { useSession } from "next-auth/react"
import { usePathname } from "next/navigation"
import AppBar from "./AppBar"
import Banners from "./Home/Banners"
import NavBar from "./NavBar"

const ConditionalLayout = () => {
    const { data: session } = useSession();
    const { isAuthenticated } = useRoleBasedRoute()
    const pathname = usePathname()

    return (
        <>
            {session?.user?.roleId && isAuthenticated && pathname !== '/' ?
                <div className="mb-[5rem]">
                    <NavBar />
                </div>
                :
                <>
                    <AppBar />
                    <div className="w-full mt-[3.9rem] overflow-hidden">
                        <Banners />
                    </div>
                </>
            }
        </>
    )
}
export default ConditionalLayout