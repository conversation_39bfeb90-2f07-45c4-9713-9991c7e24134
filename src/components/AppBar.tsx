"use client"

import Link from "next/link"
import Login from "./auth/Login"

const navList = [
    {
        id: 1,
        name: 'Home',
        route: '/',
    },
    {
        id: 2,
        name: 'About Us',
        route: '/about-us',
    },
    {
        id: 3,
        name: 'Onboarding',
        route: '',
    },
    {
        id: 4,
        name: 'Contact',
        route: '/contact',
    },
]

const intakeList = [
    {
        id: 1,
        name: 'Athlete Intake',
        route: '/athlete-intake',
    },
    {
        id: 2,
        name: 'Coach Intake',
        route: '/coach-intake',
    },
    {
        id: 3,
        name: 'Sports Org Intake',
        route: '/sports-org-intake',
    },
]

const AppBar = () => {
    const fitleredNavList = navList?.filter(each => each.name !== 'Onboarding')

    return (
        <>
            <nav className="h-15 p-1 bg-primary fixed top-0 left-0 right-0 w-full z-50">
                <div className={`px-3 sm:px-8 md:px-16 lg:px-36 xl:px-56 flex justify-between items-center text-white`}>
                    <Link href={'/'}>
                        <img src="/connectathlete-logo.svg" className="object-fill" />
                    </Link>

                    {/* <ul className="hidden lg:flex items-center font-bold gap-8">
                        {navList?.map((navItem) =>
                            navItem?.name === "Onboarding" ? (
                                <li key={navItem?.id}>
                                    <HoverCard>
                                        <HoverCardTrigger className="flex gap-1 cursor-pointer">
                                            {navItem?.name} <ChevronDown strokeWidth={3} />
                                        </HoverCardTrigger>
                                        <HoverCardContent className="flex flex-col gap-3 w-full p-5">
                                            {intakeList?.map((each) => (
                                                <Link key={each?.id} href={each?.route}>
                                                    <li className="font-bold text-primary cursor-pointer hover:text-orange-600">{each?.name}</li>
                                                </Link>
                                            ))}
                                        </HoverCardContent>
                                    </HoverCard>
                                </li>
                            ) : (
                                <li key={navItem?.id}>
                                    <Link href={navItem?.route}>{navItem?.name}</Link>
                                </li>
                            )
                        )}
                    </ul> */}

                    <div className="hidden lg:flex gap-5">
                        <Login />
                        {/* <Button variant="outline" className="bg-transparent text-white font-bold  hover:bg-secondary hover:text-white hover:border-secondary">Join Us</Button> */}
                    </div>

                    {/* Mobile and Tab view */}
                    <div className="flex lg:hidden gap-3 items-center">
                        <Login />
                        {/* <DropdownMenu>
                            <DropdownMenuTrigger className="flex gap-1 border-none outline-none"> <Menu /></DropdownMenuTrigger>
                            <DropdownMenuContent className="flex flex-col p-3 gap-2">
                                {[...fitleredNavList, ...intakeList]?.map(each => (
                                    <Link key={each?.id} href={each?.route}>
                                        <DropdownMenuItem className="font-bold cursor-pointer text-primary hover:text-orange-500">{each?.name}</DropdownMenuItem>
                                    </Link>
                                ))}
                            </DropdownMenuContent>
                        </DropdownMenu> */}
                    </div>
                </div>
            </nav>
        </>
    )
}
export default AppBar