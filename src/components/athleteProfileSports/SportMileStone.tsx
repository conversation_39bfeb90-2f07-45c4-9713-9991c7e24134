'use client'
import AddMileStoneVicoryVault from "@/components/common/AddMileStoneVicoryVault";
import { Switch } from "@/components/ui/switch";
import { RootState } from "@/store";
import { handleUpdateUserInput } from "@/store/slices/athlete/athleteSportProfileSlice";
import { useDispatch, useSelector } from "react-redux";
import MileStoneItem from "../common/MileStoneItem";

interface IProps {
    sportName: string;
}
const SportMileStone = ({ sportName }: IProps) => {
    const { toggleMileStone, isAddMileStone, mileStoneData, addedMileStonesList } = useSelector((state: RootState) => state.athleteSportProfile);
    const dispatch = useDispatch()

    const handleToggleSection = () => {
        dispatch(handleUpdateUserInput({ name: 'toggleMileStone', value: !toggleMileStone }))
    }

    const handleAddModal = () => {
        dispatch(handleUpdateUserInput({ name: 'isAddMileStone', value: !isAddMileStone }))
    }

    const handleSaveMileStone = (data) => {
        dispatch(handleUpdateUserInput({ name: 'addedMileStonesList', value: [...addedMileStonesList, data] }))
    }

    return (
        <>
            <div className="bg-slate-100 rounded-lg p-4 space-y-5">
                <div className="flex items-center justify-center gap-5">
                    <h3 className="text-xl font-bold">{sportName} - Key Milestones</h3>
                    <Switch checked={toggleMileStone} onCheckedChange={handleToggleSection} />
                </div>
                {toggleMileStone ? <>
                    <div className="flex items-end justify-end">
                        <AddMileStoneVicoryVault
                            origin="Milestone"
                            open={isAddMileStone}
                            handleAddModal={handleAddModal}
                            sourceData={mileStoneData}
                            handleSaveForm={handleSaveMileStone}
                        />
                    </div>
                    {addedMileStonesList?.length ?
                        <div className="space-y-8">
                            {addedMileStonesList?.map((item, index) => (
                                <MileStoneItem item={item} key={index + 'milestone'} />
                            ))}
                        </div>
                        :
                        null
                    }
                </> : null}
            </div>
        </>
    )
}
export default SportMileStone