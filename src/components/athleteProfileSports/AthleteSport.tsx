import { RootState } from "@/store"
import { handleUpdateUserInput } from "@/store/slices/athlete/athleteSportProfileSlice"
import { generateProfileUrl } from "@/utils/routeConfig"
import { useSession } from "next-auth/react"
import { Params } from "next/dist/shared/lib/router/utils/route-matcher"
import { useDispatch, useSelector } from "react-redux"
import { toast } from "react-toastify"
import AddsContent from "../common/AddsContent"
import BackButton from "../common/BackButton"
import SportHighLightLinks from "../common/SportHighLightLinks"
import SportHighLightVideos from "../common/SportHighLightVideos"
import { Button } from "../ui/button"
import SportCard from "./SportCard"
import SportMileStone from "./SportMileStone"
import SportStats from "./SportStats"
import VictoryVault from "./VictoryVault"


interface IProps {
    params: Params
}
const AthleteSport = ({ params }: IProps) => {
    const { toggleVideoSection, latestVideoData, addedHighlightVideoList,
        highlightLinksList, toggleHighlightLinks, isEditHighlightLinks } = useSelector((state: RootState) => state.athleteSportProfile)
    const { data: session } = useSession()
    const profileUrl = session?.user && generateProfileUrl(session?.user)
    const sportName = params?.sportName === 'addSport' ? "Add New Sport" : params?.sportName
    const dispatch = useDispatch()

    const handleOnChange = (name: string, value: string | boolean) => {
        dispatch(handleUpdateUserInput({ name, value }))
    }

    const handleUpdateLatestVideoValues = (name, value) => {
        if (name === 'video' && (typeof value === 'object')) {
            if (value as File) {
                const validTypes = ['video/mp4'];
                if (validTypes.includes(value.type)) {
                    dispatch(handleUpdateUserInput({ name: 'latestVideoData', value: { ...latestVideoData, video: value } }));
                } else {
                    toast.error('Invalid file type. Please upload MP4.');
                }
            }
        } else if (name === 'toggleVideoSection') {
            dispatch(handleUpdateUserInput({ name, value }))
        } else {
            dispatch(handleUpdateUserInput({ name: 'latestVideoData', value: { ...latestVideoData, [name]: value } }));
        }
    }

    const handleSaveHighlightVideo = () => {
        dispatch(handleUpdateUserInput({ name: "addedHighlightVideoList", value: [...addedHighlightVideoList, latestVideoData] }))
        dispatch(handleUpdateUserInput({ name: 'latestVideoData', value: { title: '', aboutVideo: "", video: null } }));
    }

    const handleEditHighLightVideo = (id: number) => {
        //Pick id and Edit
    }

    const handleDeleteHighLightVideo = (id: number) => {
        //Pick id and Delete
    }

    const handleSaveHighLightLinks = (list) => {
        
    }

    return (
        <>
            <div className="grid grid-cols-1 md:grid-cols-4 flex-grow w-full p-4">
                <div className="hidden md:block md:col-span-1" />

                <div className="flex flex-col gap-8 py-10 md:col-span-2">
                    <AddsContent />

                    <BackButton />

                    <div className="flex flex-col gap-2 items-center justify-center flex-wrap">
                        <h3 className="text-xl font-bold">Athlete’s Profile - {sportName}</h3>
                        {params?.sportName === 'addSport' ? null : <div className="flex flex-col md:flex-row items-center justify-center flex-wrap gap-8 w-full">
                            <a
                                href={`https://www.connectathlete${profileUrl}/${params?.sportName}`}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="w-full md:w-auto break-words text-center text-wrap font-semibold text-xl hover:text-blue-600 hover:underline"
                            >
                                www.connectathlete{profileUrl}/{params?.sportName}
                            </a>
                            <Button variant={'destructive'}>Delete</Button>
                        </div>}
                    </div>

                    <SportCard />

                    {params?.sportName === 'addSport' ?
                        null
                        :
                        <SportStats sportName={sportName} />
                    }

                    <SportHighLightVideos
                        sportName={sportName}
                        toggleVideoSection={toggleVideoSection}
                        latestVideoData={latestVideoData}
                        addedHighLightVideoList={addedHighlightVideoList}
                        handleUpdateLatestVideoValues={handleUpdateLatestVideoValues}
                        handleSaveHighlightVideo={handleSaveHighlightVideo}
                        handleEditHighLightVideo={handleEditHighLightVideo}
                        handleDeleteHighLightVideo={handleDeleteHighLightVideo}
                    />

                    <SportHighLightLinks
                        toggleHighlightLinks={toggleHighlightLinks}
                        isEditHighlightLinks={isEditHighlightLinks}
                        highlightLinksList={highlightLinksList}
                        handleOnChange={handleOnChange}
                        handleSaveHighLightLinks={handleSaveHighLightLinks}
                    />

                    <SportMileStone sportName={sportName} />
                    <VictoryVault sportName={sportName} />
                </div>

                <div className="hidden md:block md:col-span-1" />
            </div>
        </>
    )
}
export default AthleteSport