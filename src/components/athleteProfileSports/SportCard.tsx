'use client'
import Chip from "@/components/common/Chip"
import SearchInput from "@/components/common/SearchInput"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { AppDispatch, RootState } from "@/store"
import { fetchAllSpecialities, fetchAllSportLevels, fetchAllSports } from "@/store/slices/commonSlice"
import { EachSearchItem } from "@/utils/interfaces"
import { PencilLine } from "lucide-react"
import { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"

const currentSeasonStatusList = [
    {
        value: 123,
        label: 'Active'
    },
    {
        value: 456,
        label: 'Off-season'
    },
    {
        value: 789,
        label: 'Off-season Training'
    },
    {
        value: 1011,
        label: 'Recreational'
    },
]

const SportCard = () => {
    const dispatch = useDispatch<AppDispatch>()
    const { allSportsList, allSportLevelList, allSpecilitiesList } = useSelector((state: RootState) => state.commonSlice);
    const { selectedSport, addedSportSpecilitiesList, addedSportLevelsList } = useSelector((state: RootState) => state.athleteSportProfile)

    useEffect(() => {
        dispatch(fetchAllSports())
        dispatch(fetchAllSportLevels())
    }, [])

    useEffect(() => {
        selectedSport?.value && dispatch(fetchAllSpecialities(selectedSport?.value))
    }, [selectedSport?.value])

    const handleSearchChange = (selected: EachSearchItem | null, name: string) => {
        console.log(`Selected from ${name}:`, selected)
    }

    return (
        <>
            <div className="flex flex-col gap-4 bg-slate-100 rounded-md p-4">
                <Button size={'icon'} variant={'outline'} className="self-end">
                    <PencilLine />
                </Button>

                <div className="grid grid-cols-1 md:grid-cols-3 items-end justify-between flex-wrap gap-8 mb-3">
                    <div className="md:col-span-2 flex flex-col gap-1 w-full">
                        <Label>Sport Name</Label>
                        <SearchInput
                            list={allSportsList} name="sport"
                            placeholder="Select Sport Name"
                            onChange={handleSearchChange}
                        />
                    </div>
                    <div className="flex gap-1">
                        <Label>Primary Sport</Label>
                        <Switch checked={true} />
                    </div>
                </div>

                <div className="grid grid-cols-1 items-center flex-wrap justify-center md:justify-start gap-8 w-full ">

                    <div className="flex flex-col gap-1">
                        <Label>Years Played</Label>
                        <Input className="border-slate-300 bg-[#f5faff]" placeholder="No.of years played" />
                    </div>
                    <div className="flex flex-col gap-1">
                        <Label>Current Season Status</Label>
                        <SearchInput
                            name="seasonStatus"
                            placeholder="Select Status"
                            list={currentSeasonStatusList}
                            onChange={handleSearchChange}
                        />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-4 items-start gap-2">
                        <Label className="col-span-1">Level</Label>
                        <div className="flex flex-col col-span-3">
                            <SearchInput
                                list={allSportLevelList}
                                placeholder="Select Sport Level"
                                name="sportLevel"
                                className="w-full col-span-3"
                                onChange={handleSearchChange}
                            />
                            <div className="flex flex-wrap items-center gap-3 mt-2">
                                {addedSportLevelsList?.map(each =>
                                    <Chip
                                        id={each?.levelId}
                                        label={each?.levelName}
                                        key={each?.levelId}
                                        onRemove={() => { }}
                                    />
                                )}
                            </div>
                        </div>
                    </div>


                    <div className="grid grid-cols-1 md:grid-cols-4 items-start gap-2">
                        <Label className="col-span-1 break-words text-wrap text-center">Position/Speciality</Label>
                        <div className="flex flex-col col-span-3">
                            <SearchInput
                                list={allSpecilitiesList}
                                placeholder="Select Position/Speciality"
                                name="sportSpecility"
                                className="w-full"
                                onChange={handleSearchChange}
                            />
                            <div className="flex flex-wrap items-center gap-3 mt-2">
                                {addedSportSpecilitiesList?.map(each =>
                                    <Chip
                                        id={each?.specilityId}
                                        label={each?.specilityName}
                                        key={each?.specilityId}
                                        onRemove={() => { }}
                                    />
                                )}
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-2">
                        <Label className="col-span-1">Current Team</Label>
                        <div className="flex flex-col col-span-3">
                            <Input
                                className="border-slate-300 text-primary placeholder-gray-800 bg-[#f5faff]"
                                placeholder="Current Team"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}
export default SportCard