import { RootState } from "@/store"
import { handleUpdateUserInput } from "@/store/slices/athlete/athleteSportProfileSlice"
import { useDispatch, useSelector } from "react-redux"
import AchievementItem from "../common/AchievementItem"
import AddMileStoneVicoryVault from "../common/AddMileStoneVicoryVault"
import { Switch } from "../ui/switch"

interface IProps {
    sportName: string
}
const VictoryVault = ({ sportName }: IProps) => {
    const { toggleVictoryVault, isVictoryVault, vicotryVaultData, addedVictoryVaultList } = useSelector((state: RootState) => state.athleteSportProfile)
    const dispatch = useDispatch()

    const handleToggleSection = () => {
        dispatch(handleUpdateUserInput({ name: 'toggleVictoryVault', value: !toggleVictoryVault }))
    }

    const handleAddModal = () => {
        dispatch(handleUpdateUserInput({ name: 'isVictoryVault', value: !isVictoryVault }))
    }

    const handleSaveVictoryVault = (data) => {
        dispatch(handleUpdateUserInput({ name: 'addedVictoryVaultList', value: [...addedVictoryVaultList, data] }))
    }

    return (
        <>
            <div className="bg-slate-100 rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-center gap-5">
                    <h3 className="text-xl font-bold">{sportName} - Victory Vault</h3>
                    <Switch checked={toggleVictoryVault} onCheckedChange={handleToggleSection} />
                </div>
                {toggleVictoryVault ?
                    <>
                        <div className="flex items-end justify-end">
                            <AddMileStoneVicoryVault
                                origin='Victory Vault'
                                open={isVictoryVault}
                                handleAddModal={handleAddModal}
                                sourceData={vicotryVaultData}
                                handleSaveForm={handleSaveVictoryVault}
                            />
                        </div>
                        {addedVictoryVaultList?.length >0 && addedVictoryVaultList?.map(each =>
                            <AchievementItem item={each} key={each?.title + 'vv'} />
                        )}
                    </> : null}
            </div>
        </>
    )
}
export default VictoryVault