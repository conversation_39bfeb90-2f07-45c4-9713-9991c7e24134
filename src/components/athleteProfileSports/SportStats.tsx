'use client'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { ColumnDef } from "@tanstack/react-table"
import { Pencil, Trash2 } from "lucide-react"
import { useState } from "react"
import CommonCalender from "../common/CommonCalender"
import { GlobalSortPaginationTable } from "../common/GlobalSortPaginationTable"
import { Button } from "../ui/button"
import { Label } from "../ui/label"
import { Switch } from "../ui/switch"


interface IProps {
    sportName: string
}
const SportStats = ({ sportName }: IProps) => {
    const [date, setDate] = useState<Date | undefined>(undefined)

    const allStatsNamesList = [
        { id: 1, name: "General" },
        { id: 2, name: "Advanced" },
        { id: 3, name: "Custom" },
    ];

    const users = [
        {
            seasonName: 'Fall 2024 Junior Year',
            date: "25 05, 2025",
            statsName: { id: 1, name: "General" },
            statsValueNumeric: 12,
            statsValueText: 'General',
            statsUnit: '%',
            isPublish: false,
        },
        {
            seasonName: 'Fall 2024 Junior Year',
            date: "25 05, 2025",
            statsName: { id: 1, name: "General" },
            statsValueNumeric: 12,
            statsValueText: 'General',
            statsUnit: '%',
            isPublish: false,
        },

    ];

    const columns = [
        {
            accessorKey: "seasonName",
            header: "Season Name",
            cell: (info) => info.getValue(),
        },
        {
            accessorKey: "date",
            header: "Date",
            cell: (info) => info.getValue(),
        },
        {
            accessorKey: "statsName",
            header: "Stats Name",
            cell: (info) => {
                const initial = info.getValue() as { id: number; name: string } | undefined;

                return (
                    <div className="space-x-2">
                        <Select
                            value={initial?.id?.toString() ?? ""}
                            onValueChange={(val) => {
                                const selectedOption = allStatsNamesList?.find((opt) => opt.id.toString() === val);
                                if (selectedOption) {

                                }
                            }}
                        >
                            <SelectTrigger className="border-slate-300 bg-[#f5faff]">
                                <SelectValue placeholder="Select Stats Name">
                                    {initial?.name}
                                </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                                {allStatsNamesList?.map((item) => (
                                    <SelectItem key={item?.id} value={item?.id?.toString()}>
                                        {item?.name}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                )
            },
        },
        {
            accessorKey: "statsValueNumeric",
            header: "Stats Value Numeric",
            cell: (info) => info.getValue(),
        },
        {
            accessorKey: "statsValueText",
            header: "Stats Value Text",
            cell: (info) => info.getValue(),
        },
        {
            accessorKey: "statsUnit",
            header: "Stats Unit",
            cell: (info) => info.getValue(),
        },
        {
            accessorKey: "isPublish",
            header: "Publish",
            cell: (info) => {
                return (
                    <div className="space-x-2">
                        <Switch checked={info.getValue() || false as any} />
                    </div>
                )
            },
        },
        {
            accessorKey: "actions",
            header: "Actions",
            cell: (info) => {
                return (
                    <div className="flex items-center gap-4 space-x-2">
                        <Button size="icon" variant="outline" onClick={() => alert(`Edit`)}>
                            <Pencil />
                        </Button>
                        <Button size="icon" variant="destructive" onClick={() => alert(`Delete`)}>
                            <Trash2 />
                        </Button>
                    </div>
                )
            },
        },
    ] as ColumnDef<any>[];;


    return <>
        <div className="flex flex-col justify-center  gap-6 bg-slate-100 rounded-lg p-4 ">
            <h3 className="text-xl font-bold text-center">{sportName} - Stats</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center gap-2">
                    <Label>Date</Label>
                    <CommonCalender
                        placeholder="Filter Date"
                        dateValue={date}
                        setDateFn={setDate}
                        mode="single"
                    />
                </div>
                <div className="flex items-center gap-2">
                    <Label>Publish</Label>
                    <Select >
                        <SelectTrigger className="border-slate-300 bg-[#f5faff]">
                            <SelectValue placeholder="Filter Publish" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="publish">Publish</SelectItem>
                            <SelectItem value="unPublish">Un Publish</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
            </div>
            <GlobalSortPaginationTable
                data={users}
                columns={columns}
            />
        </div>
    </>
}
export default SportStats