'use client'

import { cn } from '@/lib/utils'
import { InputHTMLAttributes, forwardRef } from 'react'
import { NumericFormat } from 'react-number-format'
import { Input } from '../ui/input'

interface NumericInputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'type' | 'defaultValue'> {
    value?: number | string
    onValueChange?: (value: number | null) => void
    defaultValue?: string | number | null
    decimalScale?: number
    allowNegative?: boolean
    prefix?: string
    suffix?: string;
    thousandSeparator?: boolean
    className?: string
}

const NumericInput = forwardRef<HTMLInputElement, NumericInputProps>(
    (
        {
            value,
            onValueChange,
            defaultValue = '',
            decimalScale = 2,
            allowNegative = false,
            prefix = '',
            suffix = '',
            thousandSeparator = true,
            className,
            ...props
        },
        ref
    ) => {
        return (
            <NumericFormat
                getInputRef={ref}
                value={value}
                defaultValue={defaultValue}
                onValueChange={(values) => {
                    const floatVal = values.floatValue ?? 0
                    onValueChange?.(values.floatValue ?? null)

                }}
                thousandSeparator={thousandSeparator}
                decimalScale={decimalScale}
                allowNegative={allowNegative}
                prefix={prefix}
                suffix={suffix}
                customInput={Input}
                type="text"
                className={cn(
                    'border px-3 py-2 rounded-md outline-none focus:ring-2 focus:ring-primary',
                    className
                )}
                {...props}
            />
        )
    }
)

NumericInput.displayName = 'NumericInput'

export default NumericInput
