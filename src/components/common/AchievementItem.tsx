import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, TooltipContent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { EachAchievementItem } from "@/utils/interfaces"
import { format } from "date-fns"
import { PencilLine, Trash2 } from "lucide-react"


interface IProps {
    item: EachAchievementItem
}

const AchievementItem = ({ item }: IProps) => {
    return (
        <Card className="rounded-lg p-5 gap-3">
            <CardHeader className="p-0">
                <CardTitle className="text-lg">{item?.title}</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-3 items-center p-0">
                <div className="md:col-span-2">
                    <span>{item?.date && format(new Date(item?.date), 'MM-dd-yyyy')}</span>
                    <p>{item?.blurb}</p>
                    <p className="text-blue-700 font-semibold mt-1">
                        {item?.tags?.map((each) => each?.name).join(", ")}
                    </p>
                    <a
                        href={item?.link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-full md:w-auto break-words text-center text-wrap font-semibold text-sm hover:text-blue-600 hover:underline"
                    >
                        {item?.link}
                    </a>
                </div>

                <div className="flex items-center justify-between">
                    {item?.file ? (
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    {item.file.type?.startsWith("image/") ? (
                                        <a
                                            href={URL.createObjectURL(item.file)}
                                            download={item.file.name}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            <img
                                                src={URL.createObjectURL(item.file)}
                                                alt={item.file.name}
                                                className="object-fill w-full max-w-32 cursor-pointer"
                                                onError={(e) => e.currentTarget.src = '/img-picture.svg'}
                                            />
                                        </a>
                                    ) : item.file.type?.startsWith("video/") ? (
                                        <a
                                            href={URL.createObjectURL(item.file)}
                                            download={item.file.name}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            <video
                                                controls
                                                className="w-full max-w-32 cursor-pointer object-fill"
                                                onError={(e) => e.currentTarget.src = '/video.svg'}
                                            >
                                                <source
                                                    src={URL.createObjectURL(item.file)}
                                                    type={item.file.type}
                                                />
                                                Your browser does not support the video tag.
                                            </video>
                                        </a>
                                    ) : item.file.type === "application/pdf" ? (
                                        <a
                                            href={URL.createObjectURL(item.file)}
                                            download={item.file.name}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            <embed
                                                src={URL.createObjectURL(item.file)}
                                                type="application/pdf"
                                                className="w-full max-w-32 cursor-pointer"
                                                onError={(e) => e.currentTarget.src = '/pdf-file.svg'}
                                            />
                                        </a>
                                    ) : (
                                        <p className="text-gray-600 text-sm px-4 text-center">
                                            Unsupported file type: {item.file.type}
                                        </p>
                                    )}
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p>{item.file.name}</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    ) : null}

                </div>
            </CardContent>
            <CardFooter className="flex items-center justify-between p-0 w-full">
                <Button size={'icon'} variant={'outline'}>
                    <PencilLine />
                </Button>
                <Button size={'icon'} variant={'destructive'}>
                    <Trash2 />
                </Button>
            </CardFooter>
        </Card >
    )
}
export default AchievementItem