import { Trash2 } from "lucide-react";
import { Button } from "../ui/button";

interface IProps {
    item: any;
    handleDelete: (id: number) => void
}

const StateLocationItem = ({ item, handleDelete }: IProps) => {
    return (
        <>
            <li
                className="grid grid-cols-3 w-full items-center justify-between gap-6 flex-wrap bg-slate-200 rounded-lg py-2 px-2"
                key={item?.stateId}
            >
                <h6 className="col-span-2 text-wrap font-semibold">
                    <span className="font-semibold text-secondary">{item?.stateName}</span>
                    {" "} -  {" "}{item?.locationNames?.length &&
                        item?.locationNames?.join(" | ")}
                </h6>

                <div className="col-span-1 flex items-center justify-end">
                    <Button size={'icon'} variant={'destructive'} onClick={() =>
                        handleDelete(item?.stateId)
                    }>
                        <Trash2
                            size={20}
                            className="cursor-pointer"
                        />
                    </Button>
                </div>
            </li>
        </>
    )
}
export default StateLocationItem