"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { EachQuickLinkItem } from "@/utils/interfaces"
import { Check<PERSON><PERSON><PERSON>, <PERSON>ci<PERSON>Line, Trash2 } from "lucide-react"
import AddQuickLink from "./AddQuickLink"


interface IProps {
    list: EachQuickLinkItem[]
    toggleQuickLinkSection: boolean;
    onChangeToggleSection: (checked: boolean) => void
}

const QuickLinks = ({ list, toggleQuickLinkSection, onChangeToggleSection }) => {


    return (
        <div className="w-full flex flex-col gap-8 bg-slate-100 p-4 rounded-lg">
            <div className="flex items-center justify-center gap-4">
                <AddQuickLink />
                <h3 className="font-bold text-xl text-center">Quick Links</h3>
                <Switch
                    name='toggleQuickLinkSection'
                    checked={toggleQuickLinkSection}
                    onCheckedChange={onChangeToggleSection}
                />
            </div>
            {toggleQuickLinkSection ?
                <div className="flex flex-col gap-4 ">
                    {list?.map(item => (
                        <div key={item?.id} className="w-full flex items-center gap-5">
                            <div className='w-full flex items-center gap-1 justify-between bg-slate-200 rounded-xl'>
                                {item?.isEditable ?
                                    <Input value={item?.link} className="border-slate-500" /> :
                                    <p className="pl-3">{item?.link}</p>}
                                {item?.isEditable ?
                                    <Button variant={'outline'} size={'icon'}>
                                        <CheckCheck />
                                    </Button> :
                                    <Button variant={'outline'} size={'icon'}>
                                        <PencilLine />
                                    </Button>
                                }
                            </div>
                            <Button variant={'destructive'} size={'icon'} className="hover:text-secondary">
                                <Trash2 />
                            </Button>
                        </div>
                    ))}
                </div> : null}
        </div>
    )
}
export default QuickLinks