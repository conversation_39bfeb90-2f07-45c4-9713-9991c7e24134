'use client'
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { EachSocialMediaItem } from "@/utils/interfaces";
import { CheckCheck, PencilLine } from "lucide-react";


interface IProps {
    toggleSocialMediaSection: boolean;
    onChangeToggleSection: (checked: boolean) => void
    list: EachSocialMediaItem[]
}

const SocialMedia = ({ toggleSocialMediaSection, onChangeToggleSection, list }: IProps) => {
    return (
        <div className="w-full flex flex-col gap-8 bg-slate-100 p-4 rounded-lg">
            <div className="flex items-center justify-center gap-4">
                <h3 className="font-bold text-xl text-center">Social Media</h3>
                <Switch
                    name='toggleSocialMediaSection'
                    checked={toggleSocialMediaSection}
                    onCheckedChange={onChangeToggleSection}
                />
            </div>
            {toggleSocialMediaSection ?
                <div className="flex flex-col justify-between gap-4 ">
                    {list?.map(item => (
                        <div key={item?.id} className="w-full flex items-center gap-3">
                            <img src={item?.icon} alt={item?.id} className="h-8 w-8" />
                            <div className='w-full flex items-center gap-1 justify-between bg-slate-200 rounded-xl'>
                                {item?.isEditable ? <Input value={item?.link} className="border-slate-500" /> : <p className="pl-3">{item?.link}</p>}
                                {item?.isEditable ?
                                    <Button variant={'outline'} size={'icon'}>
                                        <CheckCheck />
                                    </Button> :
                                    <Button variant={'outline'} size={'icon'}>
                                        <PencilLine />
                                    </Button>
                                }
                            </div>
                            <Switch checked={item?.isEnable} />
                        </div>
                    ))}
                </div> : null}
        </div>
    )
}
export default SocialMedia