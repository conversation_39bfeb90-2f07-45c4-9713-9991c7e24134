import React from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

interface DateRangePickerProps {
  startDate: Date | null;
  endDate: Date | null;
  onDateChange: (dates: [Date | null, Date | null]) => void;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  startDate,
  endDate,
  onDateChange,
}) => {
  return (
    <div className="w-full">
      <DatePicker
        selected={startDate}
        onChange={(dates) => {
          if (Array.isArray(dates)) {
            onDateChange(dates as [Date | null, Date | null]);
          } else {
            // Handle single date selection
            onDateChange([dates, null]);
          }
        }}
        startDate={startDate}
        endDate={endDate}
        selectsRange
        placeholderText="Select Date Range"
        dateFormat="yyyy-MM-dd"
        className="w-full px-4 py-3 text-sm text-gray-700 bg-white border-2 border-gray-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 hover:border-gray-300"
        wrapperClassName="w-full"
        calendarClassName="shadow-xl border-0 rounded-xl"
        popperClassName="z-50"
        showPopperArrow={false}
        isClearable
      />
    </div>
  );
};

export default DateRangePicker;
