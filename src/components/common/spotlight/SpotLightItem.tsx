import {
    <PERSON>,
    <PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON><PERSON>,
    CardTitle
} from "@/components/ui/card";

interface IProps {
    item: {
        id: number;
        title: string;
        sport: string;
        date: string;
    }
}

const SpotLightItem = ({ item }: IProps) => {

    return (
        <>
            <Card>
                <CardHeader>
                    <CardTitle className="font-bold">{item?.title}</CardTitle>
                </CardHeader>
                <CardContent className="flex justify-between flex-wrap gap-3">
                    <span className="font-semibold text-md">{item?.sport}</span>
                    <span className="text-secondary text-md font-semibold">{item?.date}</span>
                </CardContent>
            </Card>
        </>
    )
}
export default SpotLightItem