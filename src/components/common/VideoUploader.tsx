import { Plus, X } from "lucide-react";
import { Button } from "../ui/button";
import { Label } from "../ui/label";

interface IProps {
    value: File | null | undefined;
    width?: string;
    name: string
    handleAdd: (file: File | null) => void
    handleRemove?: () => void
}
const VideoUploader = ({ value, handleAdd, width, name, handleRemove }: IProps) => {

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0] || null;
        if (handleAdd) {
            handleAdd(file);
        }
    };

    return (
        <>
            {value ?
                <>
                    <div className="bg-slate-200 text-primary w-1/2 p-5 px-3 rounded-xl flex flex-col gap-3">
                        <div className="relative w-full max-h-[250px]">
                            <Button
                                size={'icon'}
                                variant={'destructive'}
                                onClick={handleRemove}
                                className="absolute top-2 right-2 z-10"
                            >
                                <X />
                            </Button>

                            {value && (
                                <video
                                    src={URL.createObjectURL(value)}
                                    controls
                                    autoPlay
                                    className={`aspect-auto object-contain max-h-[350px] rounded-lg border`}
                                />
                            )}
                        </div>

                        <span className="text-center font-bold text-sm break-words">
                            {value?.name}
                        </span>
                    </div>
                </>
                :
                <>
                    <Label
                        htmlFor={name}
                        className={`${width} aspect-square
                            p-3 bg-white border-2 border-dashed border-gray-300 h-full object-cover
                                    rounded-lg flex flex-col items-center justify-center cursor-pointer transition-all duration-300 hover:border-slate-500 hover:bg-slate-50 group`}>
                        <Plus
                            size={24}
                            className='text-slate-400 transition-colors duration-300 group-hover:text-slate-500'
                        />
                        <span className='mt-2 text-sm text-center text-gray-500 transition-colors duration-300 group-hover:text-slate-500'>
                            Click to upload video
                        </span>
                    </Label>
                </>}
            <input
                id={name}
                type='file'
                accept='video/mp4'
                onChange={handleChange}
                className='hidden'
                name={name}
            />
        </>
    )
}
export default VideoUploader