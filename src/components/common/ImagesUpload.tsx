import { Plus, X } from 'lucide-react';
import { useRef } from 'react';
import { toast } from 'react-toastify';

const ImagesUpload = ({
    value,
    onChange,
    maxImages = 8,
    maxSize = 1024,
    width = '100%',
    height = 'auto',
    name = 'Image',
    imageNameRequired = false,
}) => {
    const fileInputRef = useRef<HTMLInputElement | null>(null);

    const handleAddClick = () => {
        fileInputRef?.current?.click();
    };

    const handleFileChange = async (event) => {
        const files = Array.from(event.target.files);

        const validFiles = files.filter((file: any) => {
            if (file.size > maxSize * 1024 * 1024) {
                toast.warn(`${file.name} exceeds the maximum size of ${maxSize} MB.`);
                return false;
            }
            return true;
        });

        const processedFiles = await Promise.all(
            validFiles.map(
                (file: any) =>
                    new Promise((resolve, reject) => {
                        const reader = new FileReader();
                        reader.onload = () => {
                            const base64String = (reader.result as string).split(',')[1];
                            resolve({
                                name: file.name,
                                url: URL.createObjectURL(file),
                                base64: base64String,
                            });
                        };
                        reader.onerror = (error) => reject(error);
                        reader.readAsDataURL(file);
                    }),
            ),
        );

        onChange([...value, ...processedFiles]);
        event.target.value = '';
    };

    const handleRemoveImage = (index, item) => {
        const updatedImages = value?.filter((_, i) => i !== index);
        onChange(updatedImages);
    };

    return (
        <div style={{ width, height }} className='rounded-lg p-4'>
            <div
                className={`grid ${maxImages === 1 ? 'grid-cols-1' : 'grid-cols-1 sm:grid-cols-3 md:grid-cols-4'
                    } gap-4`}>
                {value?.length > 0 &&
                    value?.map((file, index) => (
                        <div className='flex flex-col items-center gap-3'>
                            <div
                                key={index}
                                className={`relative group ${maxImages === 1 ? `max-w-md aspect-square` : `aspect-square w-full h-full`
                                    } `}>
                                <img
                                    src={file?.url || file?.file_path}
                                    alt={`Image ${index + 1}`}
                                    className='w-full h-full object-fill rounded-lg shadow-md transition-all duration-300 group-hover:opacity-75'
                                />
                                <button
                                    onClick={() => handleRemoveImage(index, file)}
                                    className='absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300'>
                                    <X size={16} />
                                </button>
                            </div>
                            {imageNameRequired && <p className='font-bold text-lg'>{file?.name}</p>}
                        </div>
                    ))}
                {value?.length < maxImages && (
                    <div
                        onClick={handleAddClick}
                        className={`${maxImages === 1 ? `${width} aspect-square` : `aspect-square`
                            } p-3 bg-white border-2 border-dashed border-gray-300 w-full h-full object-cover
						rounded-lg flex flex-col items-center justify-center cursor-pointer transition-all duration-300 hover:border-slate-500 hover:bg-slate-50 group`}>
                        <Plus
                            size={24}
                            className='text-slate-400 transition-colors duration-300 group-hover:text-slate-500'
                        />
                        <span className='mt-2 text-sm text-center text-gray-500 transition-colors duration-300 group-hover:text-slate-500'>
                            Click to upload image
                        </span>
                    </div>
                )}
            </div>
            <input
                ref={fileInputRef}
                type='file'
                accept='image/*'
                multiple
                onChange={handleFileChange}
                className='hidden'
                name={name}
            />
        </div>
    );
};

export default ImagesUpload;
