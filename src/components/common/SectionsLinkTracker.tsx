'use client'

import { Label } from "@/components/ui/label";


interface IProps {
    linkTrackList: { id: string, img: string, label: string, }[]
}
const SectionsLinkTracker = ({ linkTrackList }: IProps) => {

    const handleScroll = (id: string) => {
        const element = document.getElementById(id);
        if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
        }
    }

    return (
        <div className="bg-slate-100 p-4 rounded-xl grid grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-8">
            {linkTrackList?.map(item =>
                <div
                    onClick={() => handleScroll(item.id)}
                    className="flex flex-col gap-1 items-center justify-center cursor-pointer"
                >
                    <img src={item.img} className="w-8 h-8" alt={item.label} />
                    <Label className="text-[15px] font-semibold text-center">{item.label}</Label>
                </div>
            )}
        </div>
    );
};

export default SectionsLinkTracker;
