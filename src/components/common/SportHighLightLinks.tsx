import { PencilLine } from "lucide-react";
import { useEffect, useState } from "react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Switch } from "../ui/switch";

interface IHighlightLink {
    text: string;
    url: string;
}

interface IProps {
    toggleHighlightLinks: boolean;
    highlightLinksList: IHighlightLink[];
    isEditHighlightLinks: boolean;
    handleOnChange: (name: string, value: any) => void;
    handleSaveHighLightLinks: (list: { text: string, url: string }[]) => void;
}

const SportHighLightLinks = ({
    toggleHighlightLinks,
    isEditHighlightLinks,
    highlightLinksList,
    handleOnChange,
    handleSaveHighLightLinks,
}: IProps) => {
    const [localList, setLocalList] = useState(highlightLinksList);

    useEffect(() => {
        if (isEditHighlightLinks) {
            setLocalList([...highlightLinksList]);
        }
    }, [isEditHighlightLinks, highlightLinksList]);

    const handleChange = (index: number, field: 'text' | 'url', value: string) => {
        const updated = [...localList]
        updated[index] = { ...updated[index], [field]: value };
        setLocalList(updated);
    };

    const handleSave = () => {
        handleSaveHighLightLinks(localList)
        handleCancel()
    }

    const handleCancel = () => {
        handleOnChange("isEditHighlightLinks", false);
    };

    return (
        <div className="bg-slate-100 rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-center gap-5">
                <h3 className="text-xl font-bold">Highlight Links</h3>
                <Switch
                    checked={toggleHighlightLinks}
                    onCheckedChange={(checked) => handleOnChange("toggleHighlightLinks", checked)}
                />
            </div>

            {toggleHighlightLinks && (
                <>
                    <div className="flex items-center justify-end">
                        <Button
                            size="icon"
                            variant="outline"
                            onClick={() => handleOnChange("isEditHighlightLinks", !isEditHighlightLinks)}
                        >
                            <PencilLine />
                        </Button>
                    </div>

                    {localList?.map((item, index) => (
                        <div key={index + 'hll'} className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {isEditHighlightLinks ? (
                                <>
                                    <Input
                                        name='text'
                                        value={item?.text}
                                        placeholder="Link Text"
                                        onChange={(e) => handleChange(index, "text", e.target.value)}
                                    />
                                    <Input
                                        name='url'
                                        value={item?.url}
                                        placeholder="Add URL"
                                        onChange={(e) => handleChange(index, "url", e.target.value)}
                                    />
                                </>
                            ) : (
                                <>
                                    <p className="h-10 w-full p-2 bg-slate-200 break-words rounded-md">{item.text}</p>
                                    <p className="h-full w-full p-2 bg-slate-200 break-words rounded-md">
                                        <a
                                            href={`${item?.url}`}
                                            target="_blank"
                                            rel="noopener noreferrer"
                                        >
                                            {item?.url?.length > 0 ? `${item?.url?.slice(0, 35)}...` : null}
                                        </a>
                                    </p>
                                </>
                            )}
                        </div>
                    ))}

                    {isEditHighlightLinks && (
                        <div className="flex justify-end gap-3 pt-4">
                            <Button variant="outline" onClick={handleCancel}>
                                Cancel
                            </Button>
                            <Button onClick={handleSave}>Save</Button>
                        </div>
                    )}
                </>
            )}
        </div>
    );
};

export default SportHighLightLinks;