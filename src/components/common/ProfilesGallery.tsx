import { ProfileGalleryItem } from "@/utils/interfaces"
import { PencilLine } from "lucide-react"
import { Button } from "../ui/button"
import { Input } from "../ui/input"
import { Switch } from "../ui/switch"
import ImageUpload from "./ImagesUpload"

interface IProps {
    toggleGallery: boolean;
    handleToggleSection: (name: string, check: boolean) => void;
    list: ProfileGalleryItem[]
}

const ProfilesGallery = ({ toggleGallery, handleToggleSection, list }: IProps) => {
    return (
        <>
            <div className="w-full h-full flex flex-col items-center gap-3 bg-slate-100 p-4 rounded-lg">
                <div className="flex items-center justify-center gap-4">
                    <h3 className="font-bold text-xl text-center">Gallery</h3>
                    <Switch
                        name='toggleGallery'
                        checked={toggleGallery}
                        onCheckedChange={(checked) => handleToggleSection('toggleGallery', checked)}
                    />
                </div>
                {toggleGallery ?
                    <div className="flex flex-col gap-4 ">
                        {list?.map(each => (
                            <div className="flex flex-col " key={each?.id}>
                                <ImageUpload
                                    value={[]}
                                    onChange={() => { }}
                                    maxImages={1}
                                    maxSize={1024}
                                    name={each?.title}
                                    width="max-w-38"
                                />
                                <div className="bg-slate-200 w-full rounded-lg p-1 px-2 flex justify-between items-center gap-4">
                                    {each?.isEditable ?
                                        <Input placeholder="Image Title" value={each?.title} className="w-full border-slate-300" />
                                        :
                                        <p>{each?.title}</p>}
                                    <Button variant={'outline'} size={'icon'}>
                                        <PencilLine />
                                    </Button>
                                </div>
                            </div>
                        ))}
                    </div> : null}
            </div>
        </>
    )
}
export default ProfilesGallery