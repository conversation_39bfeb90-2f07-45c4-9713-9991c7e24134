import React, { useEffect, useRef, useState } from "react";

interface ProfileImgUploaderProps {
    initialUrl?: string;
    onChange?: (file: File | null) => void;
}

const ProfileImgUploader: React.FC<ProfileImgUploaderProps> = ({
    initialUrl = "/user.svg",
    onChange,
}) => {
    const [previewUrl, setPreviewUrl] = useState<string>(initialUrl);
    const inputRef = useRef<HTMLInputElement>(null);

    useEffect(() => {
        setPreviewUrl(initialUrl);
    }, [initialUrl]);

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            const localPreview = URL.createObjectURL(file);
            setPreviewUrl(localPreview);
            onChange?.(file);
        }
    };

    return (
        <div className="flex flex-col items-center gap-2">
            <input
                type="file"
                accept="image/*"
                ref={inputRef}
                onChange={handleFileChange}
                className="hidden"
            />
            <div
                onClick={() => inputRef.current?.click()}
                className="cursor-pointer"
            >
                <img
                    src={previewUrl}
                    alt="Profile"
                    className="h-36 w-36 rounded-full object-cover border"
                />
            </div>
        </div>
    );
};

export default ProfileImgUploader;
