
'use client'
import { AppDispatch, RootState } from "@/store"
import { fetchAllLocations, fetchAllStates } from "@/store/slices/commonSlice"
import { AddedStateLocationsItem, Option } from "@/utils/interfaces"
import { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import Select from "react-select"
import { toast } from "react-toastify"
import { Button } from "../ui/button"
import { Label } from "../ui/label"
import { Separator } from "../ui/separator"
import { Switch } from "../ui/switch"
import StateLocationItem from "./StateLocationItem"

interface IProps {
    roleId?: number;
    openVirtualSession: boolean;
    handleToggleSession: (checked: boolean) => void;
    selectedState: Option | null;
    selectedLocations: Option[]
    handleStateLocations: (name: string, value: any) => void;
    addedStateLocationsList: AddedStateLocationsItem[];
    handleUpdateAddedStateLocations: (list: AddedStateLocationsItem[]) => void
}

const PreferedLocations = ({ openVirtualSession,
    handleToggleSession,
    selectedState,
    selectedLocations,
    handleStateLocations,
    addedStateLocationsList,
    handleUpdateAddedStateLocations,
    roleId
}: IProps) => {
    const { allStatesList, allLocationsList } = useSelector((state: RootState) => state.commonSlice)
    const dispatch = useDispatch<AppDispatch>()

    useEffect(() => {
        dispatch(fetchAllStates())
    }, [dispatch])

    useEffect(() => {
        selectedState?.value && dispatch(fetchAllLocations(selectedState?.value))
    }, [selectedState?.value, dispatch])


    const clearStateLocation = () => {
        handleStateLocations('selectedState', null)
        handleStateLocations('selectedLocations', null)
    }

    const handleAddLocations = () => {
        if (!selectedState?.value || !selectedLocations?.length) return;
        const newLocationIds = selectedLocations?.map(location => (location.value))
        const newLocationNames = selectedLocations?.map(location => (location.label))

        const existingState = addedStateLocationsList?.find(
            (item) => item.stateId === selectedState.value
        );

        if (existingState && existingState?.locationIds?.includes(0)) {
            toast.warn("All cities are already selected for this state.");
            clearStateLocation()
            return;
        }

        if (existingState && newLocationIds?.includes(0)) {
            const updatedList = addedStateLocationsList?.map((item) => {
                if (item.stateId === selectedState?.value) {
                    return {
                        ...item,
                        locationIds: Array.from(new Set([...newLocationIds])),
                        locationNames: Array.from(new Set([...newLocationNames])),
                    };
                }
                return item;
            });
            clearStateLocation()
            handleUpdateAddedStateLocations(updatedList)
            return;
        }

        if (existingState) {
            const updatedList = addedStateLocationsList?.map((item) => {
                if (item.stateId === selectedState?.value) {
                    return {
                        ...item,
                        locationIds: Array.from(new Set([...item.locationIds, ...newLocationIds])),
                        locationNames: Array.from(new Set([...item.locationNames, ...newLocationNames])),
                    };
                }
                return item;
            });
            handleUpdateAddedStateLocations(updatedList)
            clearStateLocation()
        } else {
            const newEntry = {
                stateId: selectedState.value,
                stateName: selectedState.label,
                locationIds: newLocationIds,
                locationNames: newLocationNames,
            };
            handleUpdateAddedStateLocations([...addedStateLocationsList, newEntry])
            clearStateLocation()
        }
    };

    const handleDeleteStateLocations = (stateId) => {
        const filteredList = addedStateLocationsList?.filter(
            (item, i) => item.stateId !== stateId
        );
        handleUpdateAddedStateLocations(filteredList)
    };

    return (
        <>
            <div className="w-full flex flex-col gap-5 bg-slate-100 p-4 rounded-lg">
                <div className="flex flex-col items-center justify-center gap-4">
                    <h3 className="font-bold text-xl text-center">Preferred Locations For In-Person Sessions</h3>
                    <div className="flex items-center gap-4 bg-slate-200 rounded-lg p-4">
                        <Label htmlFor="openVirtualSession" className="font-bold">Open To Virtual Sessions</Label>
                        <Switch
                            name='openVirtualSession'
                            id={'openVirtualSession'}
                            checked={openVirtualSession}
                            onCheckedChange={handleToggleSession}
                        />
                    </div>
                </div>
                <div className="flex flex-col gap-5">
                    <div className="grid grid-cols-1 xl:grid-cols-2 w-full items-center gap-3">
                        <Select
                            name="selectedState"
                            options={allStatesList}
                            value={selectedState}
                            onChange={(selectedOption) => { handleStateLocations('selectedState', selectedOption) }}
                            isClearable
                            placeholder='Select State...'
                            className="w-full border-slate-300"
                        />
                        <Select
                            name="selectedLocations"
                            placeholder='Select Locations...'
                            options={allLocationsList}
                            isMulti
                            value={selectedLocations}
                            onChange={(selectedOption) => {
                                let selected =
                                    (selectedOption as {
                                        value: number;
                                        label: string;
                                    }[]) || [];

                                if (selected.some((city) => city.value === 0)) {
                                    selected = [{ value: 0, label: "All Cities" }];
                                } else {
                                    selected = selected.filter(
                                        (city) => city.value !== 0
                                    );
                                }
                                handleStateLocations('selectedLocations', selected)
                            }}
                        />
                    </div>
                    <Button className="self-center" onClick={handleAddLocations}>Add Locations</Button>
                </div>

                <Separator />

                <div className="flex flex-col items-center justify-center gap-5">
                    {addedStateLocationsList?.length > 0 && (
                        <>
                            <h3 className="font-semibold text-lg">Saved Location Preferences:</h3>
                            <ul className="flex gap-3 flex-col w-full items-center">
                                {addedStateLocationsList?.map((each, index) => {
                                    return (
                                        <StateLocationItem
                                            item={each}
                                            key={each?.stateId}
                                            handleDelete={handleDeleteStateLocations}
                                        />
                                    );
                                })}
                            </ul>
                        </>
                    )}
                </div>

            </div>
        </>
    )
}
export default PreferedLocations