import { Button } from "@/components/ui/button"
import { handleUpdateUserInput } from "@/store/slices/athlete/athleteProfileSlice"
import { EachSportItem } from "@/utils/interfaces"
import { generateProfileUrl } from "@/utils/routeConfig"
import { PencilLine } from "lucide-react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useDispatch } from "react-redux"

interface IProps {
    item: EachSportItem
    roleId?: number
}

const SportItem = ({ item, roleId }: IProps) => {
    const { data: session } = useSession()
    const profileUrl = session?.user && generateProfileUrl(session?.user)
    const dispatch = useDispatch()
    const router = useRouter()

    const handleSelectSportItem = () => {
        router.push(`${profileUrl}/${item?.sportName}`)
        dispatch(handleUpdateUserInput({ name: 'sportProfileId', value: item?.id }))
    }

    return (
        <>
            <div className="grid grid-cols-1 sm:grid-cols-5 justify-center items-start flex-wrap gap-1 bg-slate-200 rounded-lg p-4">
                <span className={`text-secondary font-semibold text-center`}>{item?.isPrimary ? 'Primary' : ''}</span>
                <div onClick={handleSelectSportItem} className="flex flex-col items-center gap-3 sm:col-span-3 cursor-pointer">
                    {roleId === 3 ? <p className="font-semibold">{item?.sportName}</p> : <p className="font-semibold">{item?.sportName} - {item?.sportLevel}</p>}
                    <p className="text-center">{item?.specilities?.map(each => each?.specilityName)?.join(" | ")}</p>
                </div>
                <div className="text-center">
                    <Button variant={'outline'} size={'icon'} onClick={handleSelectSportItem}>
                        <PencilLine />
                    </Button>
                </div>
            </div>
        </>
    )
}
export default SportItem