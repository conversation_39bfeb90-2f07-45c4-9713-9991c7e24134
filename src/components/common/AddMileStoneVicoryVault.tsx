import Chip from "@/components/common/Chip"
import CommonCalender from "@/components/common/CommonCalender"
import UploadFiles from "@/components/common/UploadFiles"
import { Button } from "@/components/ui/button"
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList
} from "@/components/ui/command"
import {
    Dialog,
    DialogContent,
    DialogTitle,
    DialogTrigger
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { EachMileStoneVictoryItem } from "@/utils/interfaces"
import { preventSpaces } from "@/utils/validations"
import { zodResolver } from '@hookform/resolvers/zod'
import { Plus } from "lucide-react"
import { useEffect, useState } from "react"
import { Controller, useForm } from "react-hook-form"
import { z } from 'zod'
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover"

interface IProps {
    origin: string
    open: boolean;
    handleAddModal: () => void;
    sourceData: Partial<EachMileStoneVictoryItem> | null;
    handleSaveForm: (data: EachMileStoneVictoryItem) => void
}


export const calendarSchema = z.discriminatedUnion("mode", [
    z.object({
        mode: z.literal("single"),
        date: z.date({ required_error: "Date is required" }),
    }),
    z.object({
        mode: z.literal("range"),
        date: z.object({
            from: z.date({ required_error: "Start date is required" }),
            to: z.date().optional(),
        }),
    }),
]);


const schema = z
    .object({
        date: z.date({
            required_error: "Date is required.",
        }),
        title: z
            .string()
            .min(1, 'Title is required'),
        blurb: z.string().min(1, 'Blurb is required'),
        link: z.string().min(1, 'Link is required').url('Invalid url'),
        tags: z
            .array(
                z.object({
                    id: z.string().min(1),
                    name: z.string().min(1),
                })
            )
            .min(1, 'Please select at least one tag')
            .max(3, 'You can select up to 3 tags'),
        file: z
            .instanceof(File)
            .refine((file) => file.size > 0, {
                message: "File is required",
            })
            .refine(
                (file) =>
                    ["image/", "video/", "application/pdf"].some((type) =>
                        file.type.startsWith(type)
                    ),
                {
                    message: "Only image, video, or PDF files are allowed",
                }
            ),
    })

const options = [
    { id: 'react', name: 'React' },
    { id: 'next', name: 'Next.js' },
    { id: 'tailwind', name: 'Tailwind' },
    { id: 'redux', name: 'Redux' },
    { id: 'ts', name: 'TypeScript' },
];

const AddMileStoneVicoryVault = ({
    origin,
    open,
    handleAddModal,
    sourceData,
    handleSaveForm
}: IProps) => {
    type FormData = z.infer<typeof schema>;

    const {
        control,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<FormData>({
        resolver: zodResolver(schema),
        defaultValues: {
            ...sourceData,
            title: sourceData?.title ?? '',
            link: sourceData?.link ?? '',
            blurb: sourceData?.blurb ?? '',
            tags: sourceData?.tags ?? [],
            file: sourceData?.file ?? undefined,
            date: sourceData?.date ? new Date(sourceData.date) : undefined,
        }
    });

    useEffect(() => {
        if (open) {
            reset();
        }
    }, [open, reset]);

    const onSubmit = async (data: FormData) => {
        handleSaveForm(data)
        reset()
        handleAddModal()
    };

    const onError = (errors: any) => {
        console.error("Form validation errors:", errors);
    };

    return (
        <>
            <Dialog open={open} onOpenChange={handleAddModal}>
                <DialogTrigger asChild>
                    <Button onClick={handleAddModal} variant="outline" className="gap-2 font-semibold hover:text-primary">
                        <Plus />
                        Add {origin}
                    </Button>
                </DialogTrigger>
                <DialogContent
                    onInteractOutside={(event) => event.preventDefault()}
                    className='w-[70vw]  max-w-full max-h-[90%] m-0 p-0 flex flex-col'>
                    <DialogTitle className='text-xl font-bold p-4 border-b'>
                        Add {origin}
                    </DialogTitle>
                    <div className='flex-grow flex flex-col overflow-y-scroll p-8'>
                        <form onSubmit={handleSubmit(onSubmit, onError)} >
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3  gap-4 ">
                                <div>
                                    <Controller
                                        name='date'
                                        control={control}
                                        render={({ field }) => (
                                            <CommonCalender
                                                placeholder={`${origin} Date`}
                                                mode='single'
                                                dateValue={field.value}
                                                setDateFn={(date) => field.onChange(date)}
                                            />
                                        )}
                                    />
                                    {errors.date && (
                                        <p className='text-red-500 text-sm mt-1'>
                                            {errors.date.message}
                                        </p>
                                    )}
                                </div>

                                <div>
                                    <Controller
                                        name='title'
                                        control={control}
                                        render={({ field }) => (
                                            <Input
                                                {...field}
                                                type={'text'}
                                                placeholder='Enter Title'
                                                onChange={(e) => {
                                                    const sanitizedValue = e.target.value
                                                        ?.trimStart()
                                                        ?.replace(preventSpaces, '');
                                                    field.onChange(sanitizedValue);
                                                }}
                                                className='mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50'
                                            />
                                        )}
                                    />
                                    {errors.title && (
                                        <p className='text-red-500 text-sm mt-1'>
                                            {errors.title.message}
                                        </p>
                                    )}
                                </div>

                                <div>
                                    <Controller
                                        name='link'
                                        control={control}
                                        render={({ field }) => (
                                            <Input
                                                {...field}
                                                type={'text'}
                                                placeholder='Enter Link'
                                                onChange={(e) => {
                                                    const sanitizedValue = e.target.value
                                                        ?.trimStart()
                                                        ?.replace(preventSpaces, '');
                                                    field.onChange(sanitizedValue);
                                                }}
                                                className='mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50'
                                            />
                                        )}
                                    />
                                    {errors.link && (
                                        <p className='text-red-500 text-sm mt-1'>
                                            {errors.link.message}
                                        </p>
                                    )}
                                </div>

                                <div className="col-span-full">
                                    <Controller
                                        name='blurb'
                                        control={control}
                                        render={({ field }) => (
                                            <Textarea
                                                {...field}
                                                placeholder='Enter Blurb'
                                                onChange={(e) => {
                                                    const sanitizedValue = e.target.value
                                                        ?.trimStart()
                                                        ?.replace(preventSpaces, '');
                                                    field.onChange(sanitizedValue);
                                                }}
                                                className='bg-[#f5faff] mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50'
                                            />
                                        )}
                                    />
                                    <div className="flex items-center justify-between">
                                        {errors.blurb && (
                                            <p className='text-red-500 text-sm mt-1'>
                                                {errors.blurb.message}
                                            </p>
                                        )}
                                        {/* <p className="text-right text-destructive self-end text-xs">Max 200 chars</p> */}
                                    </div>
                                </div>

                                <div className="col-span-full">
                                    <Controller
                                        name="tags"
                                        control={control}
                                        render={({ field }) => {
                                            const selectedTags = field.value || [];
                                            const [open, setOpen] = useState(false);

                                            const handleSelect = (id: string) => {
                                                const tag = options.find((o) => o.id === id);
                                                if (
                                                    tag &&
                                                    !selectedTags.some((t: { id: string }) => t.id === id) &&
                                                    selectedTags.length < 3
                                                ) {
                                                    field.onChange([...selectedTags, tag]);
                                                    setOpen(false);
                                                }
                                            };

                                            const handleRemove = (id: string) => {
                                                const updated = selectedTags.filter((tag: { id: string }) => tag.id !== id);
                                                field.onChange(updated);
                                            };

                                            const filteredOptions = options.filter(
                                                (option) => !selectedTags.some((tag: { id: string }) => tag.id === option.id)
                                            );

                                            return (
                                                <div className="col-span-full">
                                                    <Popover open={open} onOpenChange={setOpen}>
                                                        <PopoverTrigger asChild>
                                                            <Button
                                                                variant="outline"
                                                                role="combobox"
                                                                className="w-full justify-between bg-[#f5faff]"
                                                            >
                                                                Select upto 3 tags...
                                                            </Button>
                                                        </PopoverTrigger>
                                                        <PopoverContent align="start" className="w-full p-0">
                                                            <Command>
                                                                <CommandInput placeholder="Search tags..." />
                                                                <CommandList>
                                                                    <CommandEmpty>No tags found.</CommandEmpty>
                                                                    <CommandGroup>
                                                                        {filteredOptions.map((option) => (
                                                                            <CommandItem
                                                                                key={option.id}
                                                                                value={option.name}
                                                                                onSelect={() => handleSelect(option.id)}
                                                                            >
                                                                                {option.name}
                                                                            </CommandItem>
                                                                        ))}
                                                                    </CommandGroup>
                                                                </CommandList>
                                                            </Command>
                                                        </PopoverContent>
                                                    </Popover>

                                                    <div className="flex flex-wrap gap-2 mt-2">
                                                        {selectedTags.map((tag: { id: string; name: string }) => (
                                                            <Chip
                                                                key={tag.id}
                                                                id={tag.id}
                                                                label={tag.name}
                                                                onRemove={() => handleRemove(tag.id)}
                                                            />
                                                        ))}
                                                    </div>

                                                    {errors.tags && (
                                                        <p className="text-red-500 text-sm mt-1">{errors.tags.message}</p>
                                                    )}
                                                </div>
                                            );
                                        }}
                                    />

                                </div>

                                <div className="col-span-1">
                                    <Controller
                                        name="file"
                                        control={control}
                                        render={({ field: { value, onChange } }) => (
                                            <UploadFiles
                                                acceptType={["image/*", "video/*", "application/pdf"]}
                                                value={value}
                                                onFileSelect={onChange}
                                                handleRemove={() => onChange(null)}
                                                className="w-56"
                                            />
                                        )}
                                    />
                                </div>
                            </div>

                            <div className='flex flex-row justify-end mt-4 gap-5'>
                                <Button variant={'outline'} className='w-28 border-primary text-primary hover:text-primary' type='button' onClick={handleAddModal}>
                                    Cancel
                                </Button>
                                <Button className='w-28' type='submit'>
                                    Save
                                </Button>
                            </div>
                        </form>
                    </div>
                </DialogContent>
            </Dialog>
        </>
    )
}
export default AddMileStoneVicoryVault