import { Plus, X } from "lucide-react";
import { useRef } from "react";
import { Button } from "../ui/button";

interface IProps {
    value: File | null | undefined;
    className?: string;
    acceptType: string[];
    onFileSelect?: (file: File | null) => void;
    handleRemove: () => void;
}

const UploadFiles = ({
    className = "",
    acceptType,
    onFileSelect,
    handleRemove,
    value,
}: IProps) => {
    const inputRef = useRef<HTMLInputElement | null>(null);

    const handleClickUpload = () => {
        inputRef.current?.click();
    };

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0] || null;
        if (onFileSelect) {
            onFileSelect(file);
        }
    };

    const renderFilePreview = () => {
        const fileUrl = URL.createObjectURL(value!);

        if (value?.type === "video/mp4") {
            return (
                <video
                    src={fileUrl}
                    controls
                    className="w-full h-full object-cover rounded-lg"
                />
            );
        }

        if (value?.type?.startsWith("image/")) {
            return (
                <img
                    src={fileUrl}
                    alt="Uploaded"
                    className="w-full h-full object-fill rounded-lg"
                />
            );
        }

        if (value?.type === "application/pdf") {
            return (
                <object
                    data={fileUrl}
                    type="application/pdf"
                    className="w-full h-full rounded-lg bg-[#f5faff] text-center flex justify-center items-center p-2"
                >
                    <p className="text-sm text-gray-500">
                        PDF preview not supported.{" "}
                        <a
                            href={fileUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 underline"
                        >
                            Open PDF
                        </a>
                    </p>
                </object>
            );
        }

        return null;
    };

    return (
        <>
            <div
                className={`relative w-56 aspect-square border rounded-lg 
                    overflow-hidden flex items-center justify-center bg-[#f5faff]  ${className}`}
            >
                {value ? (
                    <>
                        <Button
                            size="icon"
                            variant="destructive"
                            onClick={handleRemove}
                            className="absolute top-2 right-2 z-10"
                        >
                            <X />
                        </Button>
                        {renderFilePreview()}
                    </>
                ) : (
                    <div
                        onClick={handleClickUpload}
                        className="flex flex-col items-center justify-center
                         text-gray-500 hover:text-slate-600 hover:bg-slate-50 
                         w-full h-full cursor-pointer border-2 border-dashed
                          border-gray-300 transition-colors rounded-lg"
                    >
                        <Plus size={24} />
                        <span className="mt-1 text-sm text-center">Click to upload</span>
                    </div>
                )}
            </div>

            <input
                ref={inputRef}
                type="file"
                accept={acceptType.join(",")}
                onChange={handleChange}
                className="hidden"
            />
        </>
    );
};

export default UploadFiles;
