'use client'
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>rigger
} from "@/components/ui/dialog"
import { generateProfileUrl } from "@/utils/routeConfig"
import { PencilLine } from "lucide-react"
import { useSession } from "next-auth/react"
import { useState } from "react"
import { useDispatch } from "react-redux"
import { Button } from "../ui/button"
import { Input } from "../ui/input"
import { Switch } from "../ui/switch"

interface IProps {
    sportUrl?: string
}

const ProfileUrl = ({ sportUrl }: IProps) => {
    const [profileUrl, setProfileUrl] = useState("");
    const { data: session } = useSession()
    const profileUrlQuery = session?.user && generateProfileUrl(session?.user)
    const profileCompleteUrl = sportUrl ? `https://www.connectathlete${profileUrlQuery}/${sportUrl}` : `https://www.connectathlete${profileUrlQuery}`
    const dispatch = useDispatch()

    const handleSave = () => {
        if (profileUrl) {
            console.log("Saved URL:", profileUrl);
        }
    };

    return (
        <>
            <div className="flex flex-col gap-2 justify-center">
                <div className="flex items-center justify-center gap-2">
                    <a
                        href={profileCompleteUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-full md:w-auto break-words text-center text-wrap font-semibold text-xl hover:text-blue-600 hover:underline"
                    >
                        {profileCompleteUrl}
                    </a>
                    {!sportUrl ? <Dialog>
                        <DialogTrigger asChild>
                            <Button size="icon" variant="outline">
                                <PencilLine />
                            </Button>
                        </DialogTrigger>

                        <DialogContent className="sm:max-w-md">
                            <DialogHeader>
                                <DialogTitle>Edit Profile Name</DialogTitle>
                            </DialogHeader>

                            <div className="grid gap-4 py-4">
                                <div className="grid gap-2">
                                    <label htmlFor="profile-url" className="text-sm font-medium">
                                        Your Profile Url Name
                                    </label>
                                    <Input
                                        id="profile-url"
                                        placeholder="Enter username"
                                        value={profileUrl}
                                        onChange={(e) => setProfileUrl(e.target.value)}
                                    />
                                    {profileUrl && <span className="text-sm text-green-600">Example: https://www.connectathlete.com/{session?.user?.id}/{profileUrl}</span>}
                                </div>
                            </div>

                            <DialogFooter className="flex justify-end gap-2">
                                <DialogClose asChild>
                                    <Button variant="outline">Close</Button>
                                </DialogClose>
                                <Button onClick={handleSave} disabled={!profileUrl}>Save</Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog> : null}
                </div>
                {!sportUrl ? <div className="flex gap-6 items-center  justify-center">
                    <img src="/lock-password.svg" alt='' className="h-8 fill-current cursor-pointer" />
                    <Switch className="" />
                </div> : null}
            </div>
        </>
    )
}
export default ProfileUrl