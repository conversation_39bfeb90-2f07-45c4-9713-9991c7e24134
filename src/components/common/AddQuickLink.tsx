import { Button } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON>alog<PERSON>ooter,
    <PERSON>alogHeader,
    DialogTitle,
    DialogTrigger
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Plus } from "lucide-react"

const AddQuickLink = () => {
    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button variant={'outline'} size={'icon'}>
                    <Plus />
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Add Quick Link</DialogTitle>
                </DialogHeader>
                <div className="flex flex-col gap-6 mt-5">
                    <div className="flex flex-col gap-1">
                        <Label>Quick Link</Label>
                        <Input />
                    </div>
                    <div className="flex items-center gap-1">
                        <Label htmlFor="enable">Enable/Disable</Label>
                        <Switch id="enable" />
                    </div>
                </div>
                <DialogFooter>
                    <Button type="submit">Save</Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}
export default AddQuickLink