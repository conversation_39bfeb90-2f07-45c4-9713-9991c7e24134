import VideoUploader from "@/components/common/VideoUploader"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { PencilLine, Trash2 } from "lucide-react"
import { Input } from "../ui/input"
import { Switch } from "../ui/switch"

interface IProps {
    sportName: string;
    toggleVideoSection: boolean;
    latestVideoData: any;
    addedHighLightVideoList: any[];
    handleUpdateLatestVideoValues: (name: string, value: string | File | null | boolean) => void;
    handleSaveHighlightVideo: () => void;
    handleEditHighLightVideo: (id: number) => void;
    handleDeleteHighLightVideo: (id: number) => void;
}
const SportHighLightVideos = ({
    sportName,
    toggleVideoSection,
    latestVideoData,
    addedHighLightVideoList,
    handleUpdateLatestVideoValues,
    handleSaveHighlightVideo,
    handleEditHighLightVideo,
    handleDeleteHighLightVideo
}: IProps) => {

        // const handleSaveHighlightVideo = () => {
        //     dispatch(handleCoachSportInputChange({ name: "addedHighLightVideosList", value: [...addedHighLightVideosList, { id: `${Date.now()}-${Math.floor(Math.random() * 10000)}`, ...highLightVideoData }] }))
        //     dispatch(handleCoachSportInputChange({ name: 'highLightVideoData', value: { title: '', aboutVideo: "", video: null } }));
        // }
    
        // const handleEditHighLightVideo = (id: number) => {
        //     //Pick id and Edit
        // }
    
        // const handleDeleteHighLightVideo = (id: number) => {
        //     //Pick id and Delete
        // }


    return (
        <>
            <div className="bg-slate-100 rounded-lg p-4 space-y-5">
                <div className="flex flex-wrap items-center justify-center gap-5">
                    <h3 className="text-xl font-bold text-center">{sportName} - Latest Highlight Video</h3>
                    <Switch
                        checked={toggleVideoSection}
                        onCheckedChange={(checked) => handleUpdateLatestVideoValues('toggleVideoSection', checked)}
                    />
                </div>

                {toggleVideoSection ?
                    <div className="flex flex-col gap-4">
                        <div className="grid grid-cols-1 items-center gap-3">
                            <div>
                                <Input
                                    placeholder='Video Title'
                                    value={latestVideoData?.title}
                                    onChange={(e) => handleUpdateLatestVideoValues('title', e.target.value)}
                                    name='title'
                                />
                                <p className="text-xs text-destructive text-right">Max 75 chars.</p>
                            </div>

                            <div>
                                <Input
                                    placeholder='About Video'
                                    value={latestVideoData?.aboutVideo}
                                    onChange={(e) => handleUpdateLatestVideoValues('aboutVideo', e.target.value)}
                                    name='aboutVideo'
                                    maxLength={100}
                                />
                                <p className="text-xs text-destructive text-right">Max 100 chars.</p>
                            </div>
                        </div>

                        <div className="flex items-center justify-center">
                            <VideoUploader
                                value={latestVideoData?.video}
                                handleAdd={(file) => handleUpdateLatestVideoValues('video', file)}
                                name={'uploadLatestVideo'}
                                width="w-56"
                                handleRemove={() => handleUpdateLatestVideoValues('video', null)}
                            />
                        </div>

                        <Button className="self-end w-20" onClick={handleSaveHighlightVideo}>Save</Button>

                        <div className="flex overflow-x-auto p-2 gap-5"                        >
                            {addedHighLightVideoList?.map(item => (
                                <Card className=" p-4 gap-0 rounded-lg min-w-72">
                                    <CardHeader className="p-0 text-center">
                                        <CardTitle>{item?.title}</CardTitle>
                                    </CardHeader>
                                    <CardContent className="p-0">
                                        <div className="flex flex-col justify-center items-center gap-4">
                                            {item?.video && (
                                                <video
                                                    src={URL.createObjectURL(item?.video) || ''}
                                                    controls
                                                    className="w-full max-h-[250px] rounded-lg border"
                                                />
                                            )}
                                            <p>{item?.aboutVideo}</p>
                                        </div>
                                        <Separator className="bg-slate-300 my-2" />
                                    </CardContent>
                                    <CardFooter className="flex items-center justify-between w-full gap-4 p-0">
                                        <Button size={'icon'} variant={'outline'} onClick={() => handleEditHighLightVideo(item?.id)}>
                                            <PencilLine />
                                        </Button>
                                        <Button size={'icon'} variant={'destructive'} onClick={() => handleDeleteHighLightVideo(item?.id)}>
                                            <Trash2 />
                                        </Button>
                                    </CardFooter>
                                </Card>
                            ))}
                        </div>
                    </div>
                    :
                    null}
            </div>
        </>
    )
}
export default SportHighLightVideos