"use client"

import { But<PERSON> } from "@/components/ui/button"
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from "@/components/ui/command"
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { EachSearchItem } from "@/utils/interfaces"
import { Check, ChevronDown } from "lucide-react"
import { useEffect, useState } from "react"

interface IProps {
    list: EachSearchItem[],
    placeholder?: string,
    name: string;
    className?: string;
    onChange: (selected: EachSearchItem | null, name: string) => void;
    value?: EachSearchItem | null;
}

const SearchInput = ({ list, placeholder, name, className, onChange, value }: IProps) => {
    const [open, setOpen] = useState(false)
    const [selectedItem, setSelectedItem] = useState<EachSearchItem | null>(null)

    useEffect(() => {
        value && setSelectedItem(value)
    }, [value])

    const handleSelect = (selectedValue: string) => {
        const selectedObj = list.find(item => item.value?.toString() === selectedValue) || null
        setSelectedItem(selectedObj)
        onChange(selectedObj, name)
        setOpen(false)
    }

    return (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    className={cn("w-full flex items-start justify-between", className)}
                >
                    {selectedItem?.label || placeholder || 'Select option...'}
                    <ChevronDown className="text-slate-400" />
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full">
                <Command>
                    <CommandInput placeholder={placeholder} />
                    <CommandList>
                        <CommandEmpty>No data found.</CommandEmpty>
                        <CommandGroup>
                            {list?.map((item) => (
                                <CommandItem
                                    key={item.value}
                                    value={item.value?.toString()}
                                    onSelect={handleSelect}
                                >
                                    <Check
                                        className={cn(
                                            "mr-2 h-4 w-4",
                                            selectedItem?.value?.toString() === item.value?.toString()
                                                ? "opacity-100"
                                                : "opacity-0"
                                        )}
                                    />
                                    {item.label}
                                </CommandItem>
                            ))}
                        </CommandGroup>
                    </CommandList>
                </Command>
            </PopoverContent>
        </Popover>
    )
}
export default SearchInput