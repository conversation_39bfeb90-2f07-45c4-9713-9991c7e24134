import AchievementItem from "@/components/common/AchievementItem"
import AddMileStoneVicoryVault from "@/components/common/AddMileStoneVicoryVault"
import { Switch } from "@/components/ui/switch"
import { RootState } from "@/store"
import { handleCoachSportInputChange } from "@/store/slices/coach/coachSportSlice"
import { useDispatch, useSelector } from "react-redux"

const TrackRecord = () => {
    const { toggleTrackRecord, isAddTrackRecord, trackRecordItem, addedTrackRecordsList } = useSelector((state: RootState) => state.coachSport)
    const dispatch = useDispatch()

    const handleOnChange = (name: string, value: boolean) => {
        dispatch(handleCoachSportInputChange({ name, value }))
    }

    const handleSaveTrackRecord = (data) => {
        dispatch(handleCoachSportInputChange({ name: 'addedTrackRecordsList', value: [...addedTrackRecordsList, data] }))
    }

    return (
        <>
            <div className="flex flex-col gap-8 rounded-lg p-5 bg-slate-100">
                <div className="flex items-center justify-center gap-3">
                    <h3 className="text-xl font-bold">Track Record</h3>
                    <Switch
                        checked={toggleTrackRecord}
                        onCheckedChange={(checked) => handleOnChange('toggleTrackRecord', checked)}
                    />
                </div>
                {toggleTrackRecord &&
                    <>
                        <div className="flex justify-end">
                            <AddMileStoneVicoryVault
                                origin="Track Record"
                                handleAddModal={() => handleOnChange('isAddTrackRecord', !isAddTrackRecord)}
                                open={isAddTrackRecord}
                                sourceData={trackRecordItem}
                                handleSaveForm={handleSaveTrackRecord}
                            />
                        </div>

                        {addedTrackRecordsList?.length > 0 ? addedTrackRecordsList?.map(each =>
                            <AchievementItem item={each} key={each?.title + "tR"} />
                        ) : <>
                            <div className="flex items-center justify-center">
                                <p className="font-semibold text-secondary">No Track Record Added Yet</p>
                            </div>
                        </>}
                    </>
                }
            </div>
        </>
    )
}
export default TrackRecord