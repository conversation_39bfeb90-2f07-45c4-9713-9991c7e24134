import MultiSelectWithChip from "@/components/common/MultiSelectWithChip"
import SearchInput from "@/components/common/SearchInput"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { AppDispatch, RootState } from "@/store"
import { handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { fetchAllLocations, fetchAllStates } from "@/store/slices/commonSlice"
import { Option } from "@/utils/interfaces"
import { ChangeEvent, useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"

const CoachAffiliation = () => {
    const { toggleAffiliation, allAffiliationTypesList, selectedAffiliationType,
        allCurrentAffiliations, currentAffiliation,
        allWhoAmIRightNowList, selectedWhoAmIRightNow, otherProfession, selectedIState, selectedILocations } = useSelector((state: RootState) => state.coachProfile)
    const { allStatesList, allLocationsList } = useSelector((state: RootState) => state.commonSlice)
    const dispatch = useDispatch<AppDispatch>()

    useEffect(() => {
        dispatch(fetchAllStates())
    }, [dispatch])

    useEffect(() => {
        selectedIState?.value && dispatch(fetchAllLocations(selectedIState?.value))
    }, [selectedIState, dispatch])

    const handleChangeToggle = (checked: boolean) => {
        dispatch(handleCoachInputChange({ name: 'toggleAffiliation', value: checked }))
    }

    const handleSelectChange = (name: string, selectedItem: Option | null) => {
        dispatch(handleCoachInputChange({ name, value: selectedItem }))
    }

    const handleMultiSelectChange = (name: string, selectedList: Option[]) => {
        dispatch(handleCoachInputChange({ name, value: [...selectedList] }))
    }

    const handleOnChange = (event: ChangeEvent<HTMLInputElement>) => {
        const { name, value } = event.target
        dispatch(handleCoachInputChange({ name, value }))
    }

    const isAffiliatSchoolType = selectedAffiliationType?.label?.toLowerCase() === "school" || false;
    const isOtherSelected = selectedWhoAmIRightNow?.label?.toLowerCase() === 'other' || false

    return (
        <>
            <div className="bg-slate-100 rounded-lg p-4 flex flex-col gap-5">
                <div className="flex items-center justify-center gap-3">
                    <h3 className="text-xl font-bold text-center">Coach Current Affiliation</h3>
                    <Switch checked={toggleAffiliation} onCheckedChange={handleChangeToggle} />
                </div>

                {toggleAffiliation ? <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                    <div>
                        <Label>Affiliation Type</Label>
                        <SearchInput
                            list={allAffiliationTypesList}
                            name='selectedAffiliationType'
                            onChange={(selected) => handleSelectChange('selectedAffiliationType', selected)}
                            placeholder="Select Affiliation Type..."
                        />
                    </div>

                    {isAffiliatSchoolType ?
                        <div>
                            <Label>Current Affiliation</Label>
                            <SearchInput
                                list={allCurrentAffiliations}
                                name='selectedCurrentAffiliation'
                                onChange={(selected) => handleSelectChange('selectedCurrentAffiliation', selected)}
                                placeholder="Select Current Affiliation..."
                            />
                        </div>
                        :
                        <div>
                            <Label>Current Affiliation</Label>
                            <Input
                                value={currentAffiliation}
                                placeholder="Enter Current Affiliation"
                                onChange={handleOnChange}
                                name='currentAffiliation'
                            />
                        </div>
                    }

                    <div>
                        <Label>Who am I right now?</Label>
                        <SearchInput
                            list={allWhoAmIRightNowList}
                            name='selectedWhoAmIRightNow'
                            onChange={(selected) => handleSelectChange('selectedWhoAmIRightNow', selected)}
                            placeholder="Select Options..."
                        />
                    </div>

                    {isOtherSelected ?
                        <div>
                            <Label>Other Primary Profession</Label>
                            <Input
                                name='otherProfession'
                                value={otherProfession}
                                placeholder="Enter Other Primary Professtion"
                                onChange={handleOnChange}
                            />
                        </div>
                        : null}

                    <div>
                        <Label>I State</Label>
                        <SearchInput
                            list={allStatesList}
                            name='selectedIState'
                            onChange={(selected) => handleSelectChange('selectedIState', selected)}
                            placeholder="Select State..."
                        />
                    </div>
                    <div className="">
                        <Label>I Locations(s)</Label>
                        <MultiSelectWithChip
                            options={allLocationsList}
                            value={selectedILocations}
                            name='selectedILocations'
                            onChange={(selected) => handleMultiSelectChange('selectedILocations', selected)}
                            placeholder="Select Location(s)..."
                        />
                    </div>
                </div> : null}
            </div>
        </>
    )
}
export default CoachAffiliation