'use client'

import CommonCalender from "@/components/common/CommonCalender"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RootState } from "@/store"
import { handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { EachSearchItem } from "@/utils/interfaces"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { z } from "zod"

const declarationSchema = z.object({
    accuracy: z.boolean().refine(val => val === true, { message: "You must agree to Document Accuracy & Authenticity" }),
    responsibility: z.boolean().refine(val => val === true, { message: "You must agree to Responsibility for Document Expiration" }),
    ongoing: z.boolean().refine(val => val === true, { message: "You must agree to Ongoing Verification Terms" }),
    consent: z.boolean().refine(val => val === true, { message: "You must agree to Consent to Display Verified Badge" }),
    agreeAll: z.boolean().refine(val => val === true, { message: "You must agree to all terms" }),
    eSign: z.string().min(1, "E-signature is required"),
    date: z.union([z.string().min(1), z.date()]).refine(val => !!val, { message: "Date is required" })

})

type DeclarationFormData = z.infer<typeof declarationSchema>

const CoachDeclaration = () => {
    const { declarations } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch()

    const handleOnChange = (name: string, value: string | File | null | EachSearchItem) => {
        dispatch(handleCoachInputChange({ name: 'declarations', value: { ...declarations, [name]: value } }))
    }

    const {
        register,
        handleSubmit,
        setValue,
        formState: { errors },
        watch,
    } = useForm<DeclarationFormData>({
        resolver: zodResolver(declarationSchema),
        defaultValues: declarations
    })

    const onSubmit = (data: DeclarationFormData) => {
        console.log("Declaration submitted:", data)
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="bg-slate-100 p-4 flex flex-col gap-3 rounded-lg">
            <h3 className="text-xl font-bold text-center">Coach Declaration & Acknowledgement</h3>
            <p className="p-4 font-semibold">
                Please read and confirm the following before submitting your verification documents.
            </p>
            <div className="flex flex-col p-4 gap-8 rounded-xl">
                {[
                    {
                        id: "accuracy",
                        label: "Document Accuracy & Authenticity",
                        description: "I certify that all documents I have uploaded are accurate, authentic, and belong to me. I understand that submitting false or misleading information may result in removal of my Verified Coach status or suspension from the platform.",
                    },
                    {
                        id: "responsibility",
                        label: "Responsibility for Document Expiration",
                        description: "I acknowledge that any document I upload for verification must be valid and current. If any document expires during my time on the platform, I take full responsibility to upload a renewed version in a timely manner. I understand that failure to do so may result in my Verified Coach badge being revoked.",
                    },
                    {
                        id: "ongoing",
                        label: "Ongoing Verification Terms",
                        description: "I understand that Connect Athlete does not independently verify expiration dates and relies on the information I provide. I agree to maintain the integrity of my verification status by keeping all documentation up to date.",
                    },
                    {
                        id: "consent",
                        label: "Consent to Display Verified Badge",
                        description: "By completing this verification process, I consent to having a Verified Coach badge displayed on my public profile, and I understand that my verified status is subject to review or removal at any time by the platform.",
                    },
                    {
                        id: "agreeAll",
                        label: "",
                        description: "I agree to all the terms above and certify that the information I have submitted is true and accurate.",
                    }
                ].map(({ id, label, description }) => (
                    <div key={id} className="flex gap-2 items-start">
                        <Checkbox
                            className="border-slate-500 mt-1"
                            checked={Boolean(watch(id as keyof DeclarationFormData))}
                            onCheckedChange={(checked) => setValue(id as keyof DeclarationFormData, !!checked)}
                        />
                        <Label>
                            {label && <span className="font-bold">{label}<br /></span>}
                            {description}
                            {errors[id as keyof DeclarationFormData] && (
                                <p className="text-sm text-red-500 mt-1">{errors[id as keyof DeclarationFormData]?.message}</p>
                            )}
                        </Label>
                    </div>
                ))}

                <div className="flex items-center justify-center gap-8">
                    <div className="flex flex-col gap-1">
                        <div className='flex items-center gap-1'>
                            <Checkbox className="border-slate-500" />
                            <Label>[E-Sign Name]</Label>
                        </div>
                        <Input placeholder="Enter name" {...register("eSign")} />
                        {errors.eSign && <p className="text-sm text-red-500">{errors.eSign.message}</p>}
                    </div>
                    <div className="flex flex-col gap-1">
                        <Label>[Date]</Label>
                        <CommonCalender
                            placeholder="Pick Date"
                            mode="single"
                            dateValue={declarations?.date}
                            setDateFn={(date) => handleOnChange('date', date)}
                        />
                        {errors.date && <p className="text-sm text-red-500">{errors.date.message}</p>}
                    </div>
                </div>

                <div className="flex items-center justify-center">
                    <Button type="submit">Submit</Button>
                </div>
            </div>
        </form>
    )
}

export default CoachDeclaration
