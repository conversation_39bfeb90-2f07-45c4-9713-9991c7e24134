"use client";

import Chip from "@/components/common/Chip";
import MultiSelectWithChip from "@/components/common/MultiSelectWithChip";
import ProfileImgUploader from "@/components/common/ProfileImgUploader";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { RootState } from "@/store";
import { handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice";
import { agesList, genderList } from "@/store/slices/commonSlice";
import { zodResolver } from "@hookform/resolvers/zod";
import { PencilLine } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import { z } from "zod";

const coachProfileSchema = z.object({
    firstName: z.string().min(1, "First name is required"),
    lastName: z.string().min(1, "Last name is required"),
    blurb: z.string().min(10, "Blurb must be at least 10 characters")
        .max(200, "Blurb must be at most 200 characters"),
    ageGroups: z.array(z.object({
        value: z.union([z.string(), z.number()]),
        label: z.string()
    })).min(1, "Select at least one age group"),
    genders: z.array(z.object({
        value: z.union([z.string(), z.number()]),
        label: z.string()
    })).min(1, "Select at least one gender"),
    profileImage: z.any().optional(),
});

type FormData = z.infer<typeof coachProfileSchema>;

const CoachAboutCard = () => {
    const dispatch = useDispatch();
    const { isProfileEditable } = useSelector((state: RootState) => state.coachProfile);
    const [showFull, setShowFull] = useState(false);

    const {
        register,
        control,
        handleSubmit,
        setValue,
        formState: { errors },
        watch,
        reset,
    } = useForm<FormData>({
        resolver: zodResolver(coachProfileSchema),
        defaultValues: {
            firstName: "John",
            lastName: "Doe",
            blurb: "Unlock your full potential by embracing learning every day. Small consistent steps build great habits. Stay curious, stay driven, and remember—growth starts with a single action.",
            ageGroups: [],
            genders: [],
            profileImage: undefined,
        },
    });

    const onSubmit = (data: FormData) => {
        console.log("Submit Data:", data);
        // dispatch API call here
    };

    const handleEditClick = () => {
        dispatch(handleCoachInputChange({
            name: "isProfileEditable",
            value: !isProfileEditable
        }));
    };


    const handleClickCancel = () => {
        reset()
        handleEditClick()
    }


    const blurbContent = watch("blurb")
    const isLong = blurbContent.length > 100;
    const preview = blurbContent.slice(0, 100);

    return (
        <form onSubmit={handleSubmit(onSubmit)} className="bg-slate-100 rounded-lg p-5 flex flex-col gap-5">
            <div className="grid grid-cols-1 lg:grid-cols-3 justify-center items-center gap-3">
                <div className="col-span-1 flex flex-col gap-3">
                    <ProfileImgUploader
                        initialUrl={'/user.svg'}
                        onChange={(file) => setValue("profileImage", file)}
                    />

                    {false ? <p className="text-md font-bold text-center text-secondary">Verified</p>
                        :
                        <div className="flex items-center justify-center">
                            <Button className="w-24">Verify</Button>
                        </div>
                    }
                </div>

                <div className="col-span-2 flex flex-col gap-6">
                    <div className="flex flex-col md:flex-row justify-center md:justify-between items-center gap-3">
                        {isProfileEditable ? (
                            <div className="flex items-center flex-wrap gap-3 w-full">
                                <Input {...register("firstName")} placeholder="First Name" />
                                {errors.firstName && (
                                    <p className="text-sm text-red-500">{errors.firstName.message}</p>
                                )}
                                <Input {...register("lastName")} placeholder="Last Name" />
                                {errors.lastName && (
                                    <p className="text-sm text-red-500">{errors.lastName.message}</p>
                                )}
                            </div>
                        ) : (
                            <div className="flex items-center gap-3">
                                <p className="font-semibold">{watch("firstName")}</p>
                                <p className="font-semibold">{watch("lastName")}</p>
                            </div>
                        )}

                        <Button type="button" variant="outline" size="icon" onClick={handleEditClick}>
                            <PencilLine className="h-14" />
                        </Button>
                    </div>

                    {isProfileEditable ? (
                        <div className="w-full">
                            <Textarea
                                placeholder="Write your blurb"
                                {...register("blurb")}
                                maxLength={200}
                                onInput={(e) => {
                                    const input = e.currentTarget;
                                    if (input.value.length > 200) {
                                        input.value = input.value.slice(0, 200);
                                    }
                                }}
                            />
                            {errors.blurb && (
                                <p className="text-sm text-red-500 ">{errors.blurb.message}</p>
                            )}
                        </div>
                    ) : (
                        <div className="bg-slate-200 p-4 rounded-lg">
                            <div className="text-gray-800 break-words">
                                {isLong && !showFull ? `${preview}...` : blurbContent}
                            </div>
                            {isLong && (
                                <button
                                    onClick={() => setShowFull(!showFull)}
                                    className="text-blue-500 mt-1 hover:underline text-sm"
                                    type="button"
                                >
                                    {showFull ? "Show less" : "Read more..."}
                                </button>
                            )}
                        </div>
                    )}
                </div>
            </div>

            <Separator />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
                <div className='flex flex-col gap-3'>
                    <Label>Genders I Train/Coach</Label>
                    <div>
                        {isProfileEditable ? <MultiSelectWithChip
                            name="genders"
                            control={control}
                            options={genderList}
                            placeholder="Select Genders"
                            errors={errors}
                        />
                            :
                            <div className="flex flex-wrap gap-2">
                                {(watch("genders") || []).map((gender: { label: string; value: any }) => (
                                    <Chip key={gender.value} id={gender.value} label={gender.label} />
                                ))}
                            </div>
                        }
                    </div>
                </div>

                <div className='flex flex-col gap-3'>
                    <Label>Age Groups I Train/Coach</Label>
                    <div>
                        {isProfileEditable ?
                            <MultiSelectWithChip
                                name="ageGroups"
                                control={control}
                                options={agesList}
                                placeholder="Select Age Groups"
                                errors={errors}
                            /> :
                            <div className="flex flex-wrap gap-2">
                                {(watch("ageGroups") || []).map((age: { label: string; value: any }) => (
                                    <Chip key={age.value} id={age.value} label={age.label} />
                                ))}
                            </div>
                        }
                    </div>
                </div>
            </div>

            {isProfileEditable ? <div className="flex justify-end gap-5">
                <Button variant={'outline'} onClick={handleClickCancel}>Cancel</Button>
                <Button type="submit">Save</Button>
            </div> : null}
        </form>
    );
};

export default CoachAboutCard;
