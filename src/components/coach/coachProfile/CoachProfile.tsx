'use client'
import PreferedLocations from "@/components/common/PreferedLocations"
import ProfilesGallery from "@/components/common/ProfilesGallery"
import QuickLinks from "@/components/common/QuickLinks"
import ScrollToTop from "@/components/common/ScrollToTop"
import SocialMedia from "@/components/common/SocialMedia"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { RootState } from "@/store"
import { handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { AddedStateLocationsItem, Option } from "@/utils/interfaces"
import { useDispatch, useSelector } from "react-redux"
import ProfileUrl from "../../common/ProfileUrl"
import CoachLinkTracker from "../../common/SectionsLinkTracker"
import CoachAboutCard from "./CoachAboutCard"
import CoachAffiliation from "./CoachAfifliation"
import <PERSON><PERSON><PERSON> from "./CoachBio"
import CoachCertifications from "./CoachCertifications"
import CoachContactInfo from "./CoachContactInfo"
import CoachDeclaration from "./CoachDeclaration"
import CoachFocusSection from "./CoachFocusSection"
import CoachQuestionary from "./CoachQuestionary"
import CoachResume from "./CoachResume"
import CoachVerification from "./CoachVerification"
import CoachVideo from "./CoachVideo"
import GeneralAvailability from "./GeneralAvailability"
import LatestHighLightVideo from "./LatestHighLightVideo"
import SportsInfo from "./SportsInfo"

const linkTrackList = [
    { id: 'myFocus', img: '/my-focus.png', label: 'My Focus', },
    { id: 'aboutMe', img: '/about-me.png', label: 'About Me', },
    { id: 'mySports', img: '/my-sports.webp', label: 'My Sports', },
    { id: 'media', img: '/media.png', label: 'Media', },
    { id: 'accomplisments', img: '/accomplisments.png', label: 'Accomplisments', },
    { id: 'socialMedia', img: '/social-media.png', label: 'Social Media', },
    { id: 'affiliation', img: '/affiliation.png', label: 'Affiliation', },
    { id: 'approach', img: '/approach.png', label: 'Approach', },
    { id: 'location', img: '/location.svg', label: 'Location', },
    { id: 'avaialability', img: '/availability.png', label: 'Avaialability', },
    { id: 'contact', img: '/contact.png', label: 'Contact', },
];

const CoachProfile = () => {
    const { coachSocialMediaList, toggleSocialMedia, coachQuickLinksList,
        toggleQuickLinks, toggleWebsite, website, openVirtualSession, selectedState,
        selectedLocations, coachAddedStateLocationsList, toggleGallery, galleriesList } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch()
    const handleToggleChange = (name: string, checked: boolean) => {
        dispatch(handleCoachInputChange({ name, value: checked }))
    }

    const handleStateLocations = (name: string, value: Option | Option[]) => {
        if (name === 'selectedState') {
            dispatch(handleCoachInputChange({ name, value }))
            dispatch(handleCoachInputChange({ name: 'selectedLocations', value: [] }))
        } else if (name === 'selectedLocations') {
            dispatch(handleCoachInputChange({ name, value }))
        }
    }

    const handleUpdateAddedStateLocations = (updatedList: AddedStateLocationsItem[]) => {
        dispatch(handleCoachInputChange({ name: 'coachAddedStateLocationsList', value: [...updatedList] }))
    }

    return (
        <>
            <div className="grid grid-cols-1 md:grid-cols-4 flex-grow w-full">
                <div className="hidden md:block md:col-span-1" />

                <div className="flex flex-col gap-11 py-8 col-span-1 md:col-span-2">
                    <ProfileUrl />

                    <CoachLinkTracker linkTrackList={linkTrackList} />

                    <CoachAboutCard />

                    <div id="myFocus">
                        <CoachFocusSection />
                    </div>

                    <div className="grid grid-cols-1 xl:grid-cols-2 gap-5">
                        <div className="flex flex-col gap-8" id='aboutMe'>
                            <CoachVideo />
                            <CoachBio />
                        </div>
                        <div className="flex flex-col gap-8" id='socialMedia'>
                            <SocialMedia
                                list={coachSocialMediaList}
                                toggleSocialMediaSection={toggleSocialMedia}
                                onChangeToggleSection={(checked) => handleToggleChange('toggleSocialMedia', checked)}
                            />
                            <QuickLinks
                                list={coachQuickLinksList}
                                toggleQuickLinkSection={toggleQuickLinks}
                                onChangeToggleSection={(checked) => handleToggleChange('toggleQuickLinks', checked)}
                            />

                            <div className="flex flex-col gap-5 rounded-lg p-4 bg-slate-100">
                                <div className="flex items-center justify-center gap-5">
                                    <h2>Website</h2>
                                    <Switch
                                        checked={toggleWebsite}
                                        onCheckedChange={(checked) => handleToggleChange('toggleWebsite', checked)} />
                                </div>
                                {toggleWebsite ? <Input placeholder="Enter you website" value={website} /> : null}
                            </div>
                        </div>

                    </div>

                    <div id='affiliation'>
                        <CoachAffiliation />
                    </div>

                    <CoachQuestionary />

                    <div id='mySports'>
                        <SportsInfo />
                    </div>

                    <div id='location'>
                        <PreferedLocations
                            roleId={3}
                            openVirtualSession={openVirtualSession}
                            handleToggleSession={(checked) => handleToggleChange('openVirtualSession', checked)}
                            selectedState={selectedState}
                            selectedLocations={selectedLocations}
                            handleStateLocations={handleStateLocations}
                            addedStateLocationsList={coachAddedStateLocationsList}
                            handleUpdateAddedStateLocations={handleUpdateAddedStateLocations}
                        />
                    </div>


                    <div className="grid grid-cols-1 xl:grid-cols-2 gap-5" id='media'>
                        <LatestHighLightVideo />
                        <ProfilesGallery
                            toggleGallery={toggleGallery}
                            handleToggleSection={handleToggleChange}
                            list={galleriesList}
                        />
                    </div>

                    <div id="avaialability">
                        <GeneralAvailability />
                    </div>

                    <div id='accomplisments'>
                        <CoachCertifications />
                    </div>

                    <CoachResume />

                    <div id='contact'>
                        <CoachContactInfo />
                    </div>

                    <div id='approach'>
                        <CoachVerification />
                    </div>
                    <CoachDeclaration />

                    <ScrollToTop />
                </div>

                <div className="hidden md:block md:col-span-1" />
            </div>
        </>
    )
}
export default CoachProfile