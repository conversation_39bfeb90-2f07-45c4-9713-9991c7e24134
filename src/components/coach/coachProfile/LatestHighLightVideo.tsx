'use client'
import VideoUploader from "@/components/common/VideoUploader"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { RootState } from "@/store"
import { handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { useDispatch, useSelector } from "react-redux"
import { toast } from "react-toastify"

const LatestHighLightVideo = () => {
    const { toggleHighLightVideo, highLightVideoData } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch()

    const handleToggleChange = (checked: boolean) => {
        dispatch(handleCoachInputChange({ name: 'toggleHighLightVideo', value: checked }))
    }

    const handleFileSelect = (file: File | null) => {
        if (file) {
            const validTypes = ['video/mp4'];
            if (validTypes.includes(file.type)) {
                dispatch(handleCoachInputChange({ name: 'highLightVideoData', value: { ...highLightVideoData, video: file } }));
            } else {
                toast.error('Invalid file type. Please upload MP4.');
            }
        }
    };

    const handleRemoveVideo = () => {
        dispatch(handleCoachInputChange({ name: 'highLightVideoData', value: { ...highLightVideoData, video: null } }));
    }

    return (
        <>
            <div className="flex flex-col gap-10 rounded-lg bg-slate-100 p-4">
                <div className="flex items-center justify-center gap-2">
                    <h3 className="text-xl font-bold">Latest Highlight Video</h3>
                    <Switch
                        checked={toggleHighLightVideo}
                        onCheckedChange={handleToggleChange}
                    />
                </div>

                {toggleHighLightVideo ?
                    <div className="flex flex-col gap-10">
                        <div className="flex flex-col gap-2">
                            <Label>Add Video Title</Label>
                            <Input placeholder="Video Title" />
                        </div>
                        <div className="flex flex-col gap-2">
                            <Label>Upload Video (Max limit 1 minute)</Label>
                            <VideoUploader
                                value={highLightVideoData?.video}
                                handleAdd={handleFileSelect}
                                width={`w-52`}
                                name={'highLightVideo'}
                                handleRemove={handleRemoveVideo}
                            />
                        </div>
                        <div className="flex flex-col gap-2">
                            <Label>About Video</Label>
                            <Textarea placeholder="Write about video" className="bg-[#f5faff]" />
                        </div>

                        <div className="flex justify-end items-end self-end ">
                            <Button className="w-28">Save</Button>
                        </div>
                    </div> : null}

            </div>

        </>
    )
}
export default LatestHighLightVideo