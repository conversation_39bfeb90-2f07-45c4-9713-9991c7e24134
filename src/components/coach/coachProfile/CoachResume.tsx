import UploadFiles from "@/components/common/UploadFiles"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    Card, CardAction, CardContent,
    CardHeader, CardTitle
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { RootState } from "@/store"
import { handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { Download, PencilLine } from "lucide-react"
import { useDispatch, useSelector } from "react-redux"

const CoachResume = () => {
    const { toggleResumeSection, coachResumeData, addedResumeData } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch()

    const handleToggleSection = (checked: boolean) => {
        dispatch(handleCoachInputChange({ name: 'toggleResumeSection', value: checked }))
    }

    const handleOnChange = (name: string, value: string | File | null) => {
        dispatch(handleCoachInputChange({ name: 'coachResumeData', value: { ...coachResumeData, [name]: value } }))
    }

    const handleSaveResume = () => {
        dispatch(handleCoachInputChange({ name: 'coachResumeData', value: null }))
        dispatch(handleCoachInputChange({ name: 'addedResumeData', value: { ...coachResumeData } }))
    }

    const handleEditResume = () => {
        dispatch(handleCoachInputChange({ name: 'coachResumeData', value: { ...addedResumeData } }))
        dispatch(handleCoachInputChange({ name: 'addedResumeData', value: null }))
    }

    return (
        <>
            <div className="flex flex-col gap-8 p-4 rounded-xl bg-slate-100">
                <div className="flex items-center justify-center gap-5">
                    <h2 className="font-bold text-xl">Resume
                        {" "}
                        <span className="text-red-500">(NEW)</span>
                    </h2>
                    <Switch checked={toggleResumeSection} onCheckedChange={handleToggleSection} />
                </div>

                {toggleResumeSection &&
                    <>
                        {!addedResumeData ?
                            <div className="flex flex-col gap-5">
                                <div className="flex flex-col gap-2">
                                    <Label>Title</Label>
                                    <Input
                                        value={coachResumeData?.title}
                                        name='title'
                                        placeholder="Enter Title"
                                        onChange={(event) => handleOnChange('title', event.target.value)}
                                    />
                                </div>
                                <div className="flex flex-col gap-2">
                                    <Label>Document Link</Label>
                                    <Input
                                        value={coachResumeData?.link}
                                        name='link'
                                        placeholder="Enter Document Link"
                                        onChange={(event) => handleOnChange('link', event.target.value)}
                                    />
                                </div>
                                <div className="flex flex-col gap-2">
                                    <Label>Upload Resume (Pdf)</Label>
                                    <UploadFiles
                                        acceptType={['application/pdf']}
                                        value={coachResumeData?.resume}
                                        handleRemove={() => handleOnChange('resume', null)}
                                        onFileSelect={(file) => handleOnChange('resume', file)}
                                        className="w-56"
                                    />
                                </div>

                                <div className="flex justify-end">
                                    <Button className="w-24" onClick={handleSaveResume}>Save</Button>
                                </div>
                            </div>
                            :
                            <div className="flex flex-col items-center justify-center">
                                <Card className="py-3 w-[70%]">
                                    <CardHeader>
                                        <CardTitle className="text-xl font-bold">{addedResumeData?.title}</CardTitle>
                                        <CardAction>
                                            <Button size="icon" variant="outline"
                                                onClick={handleEditResume}
                                            >
                                                <PencilLine />
                                            </Button>
                                        </CardAction>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="grid grid-cols-1 gap-3">

                                            <div className="flex flex-col gap-2">
                                                <TooltipProvider>
                                                    <Tooltip>
                                                        <TooltipTrigger asChild>
                                                            <img
                                                                src='/pdf-file.svg'
                                                                className="w-16 cursor-pointer"
                                                            />
                                                        </TooltipTrigger>
                                                        <TooltipContent>
                                                            <p>{addedResumeData?.resume?.name}</p>
                                                        </TooltipContent>
                                                    </Tooltip>
                                                </TooltipProvider>
                                                {addedResumeData?.resume && (
                                                    <a
                                                        href={URL.createObjectURL(addedResumeData?.resume)}
                                                        download={addedResumeData?.resume?.name}
                                                        className="flex items-center gap-1"
                                                    >
                                                        <Download className="w-4" />
                                                        Download
                                                    </a>
                                                )}
                                            </div>

                                            <div className="flex flex-col gap-1">
                                                <a
                                                    href={addedResumeData?.link}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-blue-600 underline"
                                                >
                                                    {addedResumeData?.link}
                                                </a>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        }
                    </>
                }
            </div>
        </>
    )
}
export default CoachResume