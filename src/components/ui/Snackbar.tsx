"use client";

import React, { useEffect, useState } from "react";
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from "lucide-react";
import clsx from "clsx";

export type SnackbarType = "success" | "error" | "info" | "warning";

export interface SnackbarProps {
  id: string;
  type: SnackbarType;
  title?: string;
  message: string;
  duration?: number;
  onClose: (id: string) => void;
  action?: {
    label: string;
    onClick: () => void;
  };
}

const Snackbar: React.FC<SnackbarProps> = ({
  id,
  type,
  title,
  message,
  duration = 10000000,
  onClose,
  action,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    // Trigger entrance animation
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        handleClose();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [duration]);

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => {
      onClose(id);
    }, 300);
  };

  const getIcon = () => {
    switch (type) {
      case "success":
        return <CheckCircle className="w-5 h-5" />;
      case "error":
        return <AlertCircle className="w-5 h-5" />;
      case "warning":
        return <AlertTriangle className="w-5 h-5" />;
      case "info":
      default:
        return <Info className="w-5 h-5" />;
    }
  };

  const getStyles = () => {
    const baseStyles = "border-l-4 shadow-lg";
    
    switch (type) {
      case "success":
        return `${baseStyles} bg-green-50 border-green-500 text-green-800`;
      case "error":
        return `${baseStyles} bg-red-50 border-red-500 text-red-800`;
      case "warning":
        return `${baseStyles} bg-yellow-50 border-yellow-500 text-yellow-800`;
      case "info":
      default:
        return `${baseStyles} bg-blue-50 border-blue-500 text-blue-800`;
    }
  };

  const getIconColor = () => {
    switch (type) {
      case "success":
        return "text-green-600";
      case "error":
        return "text-red-600";
      case "warning":
        return "text-yellow-600";
      case "info":
      default:
        return "text-blue-600";
    }
  };

  return (
    <div
      className={clsx(
        "fixed top-4 right-4 z-50 max-w-md w-full mx-4 sm:mx-0 transform transition-all duration-300 ease-in-out",
        isVisible && !isExiting
          ? "translate-x-0 opacity-100 scale-100"
          : "translate-x-full opacity-0 scale-95"
      )}
    >
      <div className={clsx("rounded-lg p-4", getStyles())}>
        <div className="flex items-start gap-3">
          {/* Icon */}
          <div className={clsx("flex-shrink-0 mt-0.5", getIconColor())}>
            {getIcon()}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            {title && (
              <h4 className="text-sm font-semibold mb-1 truncate">{title}</h4>
            )}
            <p className="text-sm leading-relaxed">{message}</p>
            
            {action && (
              <button
                onClick={action.onClick}
                className={clsx(
                  "mt-2 text-sm font-medium underline hover:no-underline transition-all",
                  type === "success" && "text-green-700 hover:text-green-800",
                  type === "error" && "text-red-700 hover:text-red-800",
                  type === "warning" && "text-yellow-700 hover:text-yellow-800",
                  type === "info" && "text-blue-700 hover:text-blue-800"
                )}
              >
                {action.label}
              </button>
            )}
          </div>

          {/* Close Button */}
          <button
            onClick={handleClose}
            className={clsx(
              "flex-shrink-0 p-1 rounded-full hover:bg-black hover:bg-opacity-10 transition-colors",
              type === "success" && "text-green-600 hover:text-green-700",
              type === "error" && "text-red-600 hover:text-red-700",
              type === "warning" && "text-yellow-600 hover:text-yellow-700",
              type === "info" && "text-blue-600 hover:text-blue-700"
            )}
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default Snackbar;
