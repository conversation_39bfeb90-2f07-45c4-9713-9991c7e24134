"use client";

import React from "react";
import { useSnackbar } from "@/contexts/SnackbarContext";
import { Button } from "@/components/ui/button";

const SnackbarDemo: React.FC = () => {
  const { showSuccess, showError, showInfo, showWarning, showSnackbar } = useSnackbar();

  const handleShowSuccess = () => {
    showSuccess("Operation completed successfully!", "Success");
  };

  const handleShowError = () => {
    showError("Something went wrong. Please try again.", "Error");
  };

  const handleShowInfo = () => {
    showInfo("Here's some helpful information for you.", "Information");
  };

  const handleShowWarning = () => {
    showWarning("Please review your settings before proceeding.", "Warning");
  };

  const handleShowCustom = () => {
    showSnackbar({
      type: "success",
      title: "Custom Snackbar",
      message: "This is a custom snackbar with an action button.",
      duration: 100000,
      action: {
        label: "View Details",
        onClick: () => {
          alert("Action clicked!");
        },
      },
    });
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-md">
      <h3 className="text-lg font-semibold mb-4">Snackbar Demo</h3>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
        <Button onClick={handleShowSuccess} className="bg-green-600 hover:bg-green-700">
          Show Success
        </Button>
        <Button onClick={handleShowError} className="bg-red-600 hover:bg-red-700">
          Show Error
        </Button>
        <Button onClick={handleShowInfo} className="bg-blue-600 hover:bg-blue-700">
          Show Info
        </Button>
        <Button onClick={handleShowWarning} className="bg-yellow-600 hover:bg-yellow-700">
          Show Warning
        </Button>
        <Button onClick={handleShowCustom} className="bg-purple-600 hover:bg-purple-700">
          Show Custom
        </Button>
      </div>
    </div>
  );
};

export default SnackbarDemo;
