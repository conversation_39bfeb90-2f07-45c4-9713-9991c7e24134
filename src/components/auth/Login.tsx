import {
    <PERSON><PERSON>,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from "@/components/ui/dialog"
import { AppDispatch, RootState } from "@/store"
import { handleLoginError, loginUser } from "@/store/slices/auth/loginSlice"
import { ROUTES } from "@/utils/routeConfig"
import { preventSpaces } from "@/utils/validations"
import { zodResolver } from '@hookform/resolvers/zod'
import { Eye, EyeOff, Loader2, UserRound } from "lucide-react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { Controller, useForm } from 'react-hook-form'
import { useDispatch, useSelector } from "react-redux"
import { z } from 'zod'
import { Button } from "../ui/button"
import { Input } from "../ui/input"

const schema = z
    .object({
        email: z
            .string()
            .min(1, 'Email is required')
            .email('Enter a valid email address'),

        password: z
            .string()
            .min(1, 'Password is required')
        // .min(8, 'Password must be at least 8 characters long')
        // .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
        // .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
        // .regex(/[0-9]/, 'Password must contain at least one number')
        // .regex(/[#?!@$%^&*-_]/, 'Password must contain at least one special character')
    })

const Login = () => {
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [showPassword, setShowPassword] = useState(false)
    const { logInData, loading, error } = useSelector((state: RootState) => state.login)
    const dispatch = useDispatch<AppDispatch>()
    type FormData = z.infer<typeof schema>;
    const router = useRouter()

    const {
        control,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<FormData>({
        resolver: zodResolver(schema),
        defaultValues: {
            ...logInData
        },
    });

    useEffect(() => {
        if (isDialogOpen) {
            reset();
        }
    }, [isDialogOpen, reset]);

    const onSubmit = async (data: FormData) => {
        const reqBody = {
            email: data?.email,
            password: data?.password,
        };
        try {
            const resultAction = await dispatch(loginUser(reqBody))
            if (loginUser.fulfilled.match(resultAction)) {
                reset();
                setIsDialogOpen(false);
                const roleId = resultAction?.payload?.user?.roleId;
                let dashboardPath = "/";
                switch (roleId) {
                    case 2:
                        dashboardPath = ROUTES.DASHBOARD.ATHLETE;
                        break;
                    case 3:
                        dashboardPath = ROUTES.DASHBOARD.COACH;
                        break;
                    case 4:
                        dashboardPath = ROUTES.DASHBOARD.BUSINESS;
                        break;
                    default:
                        dashboardPath = ROUTES.HOME;
                }
                router.push(dashboardPath)
            } else if (loginUser.rejected.match(resultAction)) {
                const errorMessage = resultAction.payload
                dispatch(handleLoginError(errorMessage));
            }
        } catch (error) {
            console.error("Login failed:", "unexpected error occured")
            dispatch(handleLoginError("Unexpected error occured"));
        }
    };

    return (
        <>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                <DialogTrigger asChild>
                    <Button className="bg-white text-primary font-bold hover:bg-secondary hover:text-white">
                        <UserRound strokeWidth={3} />
                        Login
                    </Button>
                </DialogTrigger>
                <DialogContent>
                    <DialogHeader className="space-y-10">
                        <DialogTitle className="text-3xl mx-auto mt-5 font-bold text-primary">Login</DialogTitle>
                        <DialogDescription>
                            <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-6">
                                <div className="space-y-1">
                                    <Controller
                                        name='email'
                                        control={control}
                                        render={({ field }) => (
                                            <Input
                                                {...field}
                                                type={'text'}
                                                placeholder='Email Address'
                                                onChange={(e) => {
                                                    const sanitizedValue = e.target.value
                                                        ?.trimStart()
                                                        ?.replace(preventSpaces, '');
                                                    field.onChange(sanitizedValue);
                                                }}
                                                className='text-primary border-slate-300 py-5 block w-full rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50'
                                            />
                                        )}
                                    />
                                    {errors.email && (
                                        <p className='text-red-500 text-sm text-left'>
                                            {errors.email.message}
                                        </p>
                                    )}
                                </div>

                                <div className="space-y-1">
                                    <Controller
                                        name='password'
                                        control={control}
                                        render={({ field }) => (
                                            <div className='relative'>
                                                <Input
                                                    {...field}
                                                    type={showPassword ? 'text' : 'password'}
                                                    placeholder='Your password'
                                                    onChange={(e) => {
                                                        const sanitizedValue = e.target.value
                                                            ?.trimStart()
                                                            ?.replace(preventSpaces, '');
                                                        field.onChange(sanitizedValue);
                                                    }}
                                                    className='border-slate-300 text-primary py-5 block w-full rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50'
                                                />
                                                <Button
                                                    type="button"
                                                    variant="ghost"
                                                    size="icon"
                                                    className="absolute right-2 top-1/2 -translate-y-1/2 h-8 w-8 hover:text-blue-950"
                                                    onClick={() => setShowPassword(!showPassword)}
                                                >
                                                    {showPassword ? (
                                                        <EyeOff className="h-4 w-4" />
                                                    ) : (
                                                        <Eye className="h-4 w-4" />
                                                    )}
                                                </Button>
                                            </div>
                                        )}
                                    />
                                    {errors.password && (
                                        <p className='text-red-500 text-sm  text-left'>
                                            {errors.password.message}
                                        </p>
                                    )}
                                </div>

                                <div className="flex justify-between">
                                    <span className="cursor-pointer text-primary font-semibold">Register with us</span>
                                    <span className="cursor-pointer text-primary font-semibold ">Forgot Password</span>
                                </div>

                                <div className="flex flex-col mb-4 gap-2">
                                    <Button type="submit" className="bg-primary hover:bg-primary p-5 text-md">
                                        {loading ?
                                            <>
                                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                                Loading...
                                            </>
                                            :
                                            'Submit'
                                        }
                                    </Button>
                                    {error && (
                                        <p className='text-red-500 text-sm mt-0 text-center'>
                                            {error}
                                        </p>
                                    )}
                                </div>
                            </form>
                        </DialogDescription>
                    </DialogHeader>
                </DialogContent>
            </Dialog>
        </>
    )
}
export default Login