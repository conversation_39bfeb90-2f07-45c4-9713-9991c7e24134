'use client'

import { AppDispatch, RootState } from "@/store";
import { fetchHomeBanners } from "@/store/slices/homeCMSSlice";
import { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Carousel, CarouselContent, CarouselItem } from "../ui/carousel";


const Banners = () => {
    const apiRef = useRef<any>(null);
    const [currentSlide, setCurrentSlide] = useState(0);
    const dispatch = useDispatch<AppDispatch>()
    const { homeBannerImages, loading } = useSelector((state: RootState) => state.homeCMS)

    useEffect(() => {
        dispatch(fetchHomeBanners())
    }, [dispatch])


    useEffect(() => {
        const interval = setInterval(() => {
            if (apiRef.current && homeBannerImages?.length > 1) {
                const nextIndex = (currentSlide + 1) % homeBannerImages?.length;
                apiRef.current.scrollTo(nextIndex);
                setCurrentSlide(nextIndex);
            }
        }, 3000);

        return () => clearInterval(interval);
    }, [currentSlide, homeBannerImages?.length]);

    return (
        <div className="relative w-full bg-black overflow-hidden h-auto">
            <div className="w-full h-full">
                {homeBannerImages?.length > 0 ? <Carousel
                    opts={{
                        align: "center",
                        loop: true,
                    }}
                    className="w-full h-full"
                    setApi={(api) => {
                        apiRef.current = api;
                    }}
                >
                    <CarouselContent>
                        {homeBannerImages?.map((image, index) => (
                            <CarouselItem
                                key={index}
                                className="flex justify-center items-center w-full"
                            >
                                <div className="w-full flex justify-center items-center">
                                    <img
                                        src={image?.fileLocation}
                                        alt={`Banner ${image?.fileTitle}`}
                                        loading="lazy"
                                        className="w-full object-contain  h-auto sm:object-cover max-h-screen"
                                    />
                                </div>
                            </CarouselItem>
                        ))}
                    </CarouselContent>

                    {homeBannerImages?.length > 1 && (
                        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10">
                            {homeBannerImages?.map((_, index) => (
                                <button
                                    key={index}
                                    onClick={() => {
                                        apiRef.current?.scrollTo(index);
                                        setCurrentSlide(index);
                                    }}
                                    className={`w-3 h-3 rounded-full transition-colors duration-200 ${currentSlide === index ? "bg-white" : "bg-gray-400"
                                        }`}
                                />
                            ))}
                        </div>
                    )}
                </Carousel>
                    :
                    <div className="w-full flex justify-center items-center">
                        <img
                            src={'/ca-banner.png'}
                            alt={`Banner`}
                            loading="lazy"
                            className="w-full object-contain  h-auto sm:object-cover max-h-screen"
                        />
                    </div>
                }
            </div>
        </div>
    )
}
export default Banners