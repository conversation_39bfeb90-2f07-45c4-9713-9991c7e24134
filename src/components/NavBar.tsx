import { generateProfileUrl } from "@/utils/routeConfig";
import {
  ChevronDown,
  Home,
  LogOut,
  Menu,
  UserRound,
  UserRoundCog,
} from "lucide-react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { ReactNode, useEffect, useMemo, useState } from "react";
import Avatar from "./common/Avatar";
import DropdownMenusItem from "./common/DropdownMenuItem";
import NavItem from "./common/NavItem";

export type EachNavItem = {
    id: string,
    icon: ReactNode,
    label: string | ReactNode,
    route: string,
    labelIcon?: ReactNode,
}
export type NavListItem = {
    id: string,
    icon: ReactNode,
    label: string | ReactNode,
    route: string,
    labelIcon?: ReactNode,
    dropdownMenus?: EachNavItem[]
}

const NavBar = () => {
  const [navList, setNavList] = useState<NavListItem[]>([]);
  const { data: session } = useSession();
  const profileUrl = session?.user && generateProfileUrl(session?.user);
  const commonMenusList = [
    {
      id: "changePassword",
      icon: <></>,
      label: "Change Password",
      labelIcon: <UserRoundCog className="h-4" />,
      route: "",
    },
    {
      id: "logout",
      icon: <></>,
      label: "Logout",
      labelIcon: <LogOut className="h-4" />,
      route: "",
    },
  ];

    const athleteMenusList = [
        {
            id: 'athleteHome',
            icon: <img src={'/home.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Home',
            route: '/athlete-dashboard'
        },
        {
            id: 'athleteResources',
            icon: <img src={'/resources.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Resources',
            route: ''
        },
        {
            id: 'athleteExplore',
            icon: <img src={'/explore.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Explore',
            route: ''
        },
        {
            id: 'athleteSaved',
            icon: <img src={'/saveIcon.svg'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Saved',
            route: ''
        },
        {
            id: 'athleteMessages',
            icon: <img src={'/message.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Messages',
            route: ''
        },
        {
            id: 'athleteHelp',
            icon: <img src={'/helpIcon.svg'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Help',
            route: ''
        },
        {
            id: 'athleteProfile',
            icon: <img src={'/profileIcon.svg'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Profile',
            route: profileUrl || ''
        },
        {
            id: 'athleteSettings',
            icon: <Avatar profileImg="" name={session?.user?.userFirstName?.at(0)} styles="h-8 w-8 bg-slate-700 text-white" />,
            label: "Settings",
            labelIcon: <ChevronDown className="fill-slate-700  text-slate-700" />,
            route: '',
            dropdownMenus: commonMenusList
        },
    ]

    const coachMenusList = [
        {
            id: 'coachHome',
            icon: <img src={'/home.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Home',
            route: '/coach-dashboard'
        },
        {
            id: 'coachResources',
            icon: <img src={'/resources.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Resources',
            route: ''
        },
        {
            id: 'coachExplore',
            icon: <img src={'/explore.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Explore',
            route: ''
        },
        {
            id: 'coachSaved',
            icon: <img src={'/saveIcon.svg'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Saved',
            route: ''
        },
        {
            id: 'coachMessages',
            icon: <img src={'/message.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Messages',
            route: ''
        },
        {
            id: 'coachHelp',
            icon: <img src={'/helpIcon.svg'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Help',
            route: ''
        },
        {
            id: 'coachProfile',
            icon: <img src={'/profileIcon.svg'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Profile',
            route: profileUrl || ''
        },
        {
            id: 'coachSettings',
            icon: <Avatar profileImg="" name={session?.user?.userFirstName?.at(0)} styles="h-8 w-8 bg-slate-700 text-white" />,
            label: "Settings",
            labelIcon: <ChevronDown className="fill-slate-700  text-slate-700" />,
            route: '',
            dropdownMenus: commonMenusList
        },
    ]

    const sportsOrgMenusList = [
        {
            id: 'businessHome',
            icon: <img src={'/home.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Home',
            route: '/business-dashboard'
        },
        {
            id: 'businessResources',
            icon: <img src={'/resources.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Resources',
            route: ''
        },
        {
            id: 'businessExplore',
            icon: <img src={'/explore.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Explore',
            route: ''
        },
        {
            id: 'businessSaved',
            icon: <img src={'/saveIcon.svg'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Saved',
            route: ''
        },
        {
            id: 'businessMessages',
            icon: <img src={'/message.png'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Messages',
            route: ''
        },
        {
            id: 'businessHelp',
            icon: <img src={'/helpIcon.svg'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Help',
            route: ''
        },
        {
            id: 'businessProfile',
            icon: <img src={'/profileIcon.svg'} className="rounded-lg h-full object-fill w-7" />,
            label: 'Profile',
            route: profileUrl || ''
        },
        {
            id: 'businessSettings',
            icon: <Avatar profileImg="" name={session?.user?.userFirstName?.at(0)} styles="h-8 w-8 bg-slate-700 text-white" />,
            label: "Settings",
            labelIcon: <ChevronDown className="fill-slate-700  text-slate-700" />,
            route: '',
            dropdownMenus: commonMenusList
        },
    ]

  const computedNavList = useMemo(() => {
    switch (session?.user?.roleId) {
      case 2:
        return athleteMenusList;
      case 3:
        return coachMenusList;
      case 4:
        return sportsOrgMenusList;
      default:
        return [];
    }
  }, [session?.user?.roleId]);

    useEffect(() => {
        computedNavList?.length && setNavList(computedNavList);
    }, [computedNavList]);

    const filterMobileMenusList = navList?.filter(each => !each?.id?.toLowerCase().includes('settings'))

    return (
        <nav className="h-15 p-1 fixed top-0 left-0 right-0 w-full z-50  bg-slate-300">
            <div className="px-3 sm:px-8 md:px-16 lg:px-36 p-1 flex justify-between items-center ">
                <img src='/connectathlete-logo.svg' alt="Connect Athlete" />

                <ul className="hidden lg:flex items-center gap-10">
                    {navList?.map(each =>
                        each?.dropdownMenus ?
                            <DropdownMenusItem
                                triggerMenu={<NavItem item={each} />}
                                menusList={each?.dropdownMenus}
                            />

                            : (
                                <Link href={each?.route} className="flex flex-col items-center">
                                    <NavItem item={each} />
                                </Link>
                            )
                    )}
                </ul>

                {/* Mobile View */}
                <div className="lg:hidden flex flex-col gap-3">
                    <DropdownMenusItem
                        triggerMenu={<Menu className="text-white" size={24} />}
                        menusList={[...filterMobileMenusList, ...commonMenusList]}
                    />
                </div>
            </div>
        </nav>
    )
}
export default NavBar