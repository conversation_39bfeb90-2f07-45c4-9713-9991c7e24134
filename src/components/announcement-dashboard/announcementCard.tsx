import React, { useState } from "react";
import { <PERSON><PERSON><PERSON>, Trash2, <PERSON>n, <PERSON>, Tag } from "lucide-react";
import { Button } from "../ui/button";

type Announcement = {
  id: number;
  title: string;
  startDate: string;
  endDate: string;
  type: {
    id: number;
    typeName: string;
  };
  category: {
    id: number;
    categoryName: string;
  };
  isPinned: boolean;
  isActive: boolean;
};

interface Props {
  data: Announcement;
  onDelete?: (id: number) => void;
  onPin?: (id: number, isPinned: boolean) => void;
  onEdit?: (id: number) => void;
}

const AnnouncementCard: React.FC<Props> = ({ 
  data, 
  onDelete, 
  onPin, 
  onEdit 
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  const [isPinning, setIsPinning] = useState(false);

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      await onDelete?.(data.id);
    } finally {
      setIsDeleting(false);
    }
  };

  const handlePin = async () => {
    setIsPinning(true);
    try {
      await onPin?.(data.id, !data.isPinned);
    } finally {
      setIsPinning(false);
    }
  };

  const handleEdit = () => {
    onEdit?.(data.id);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-surface border border-border rounded-lg hover:shadow-md transition-all duration-200">
      {/* Header */}
      <div className="p-4 sm:p-6">
        <div className="flex items-start justify-between gap-4">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3 mb-3">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-surface-variant text-on-surface-variant">
                #{data.id}
              </span>
              {data.isPinned && (
                <div className="flex items-center gap-1 text-warning">
                  <Pin className="w-4 h-4 fill-current" />
                  <span className="text-xs font-medium">Pinned</span>
                </div>
              )}
              <span
                className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  data.isActive
                    ? "bg-success/10 text-success"
                    : "bg-destructive/10 text-destructive"
                }`}
              >
                {data.isActive ? "Active" : "Inactive"}
              </span>
            </div>
            
            <h3 className="text-lg font-semibold text-on-surface mb-3 leading-tight">
              {data.title}
            </h3>

            {/* Date and Category Info */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-on-surface-variant">
                  <Calendar className="w-4 h-4" />
                  <span className="font-medium">Duration</span>
                </div>
                <div className="text-sm text-on-surface">
                  <div>{formatDate(data.startDate)} → {formatDate(data.endDate)}</div>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-on-surface-variant">
                  <Tag className="w-4 h-4" />
                  <span className="font-medium">Classification</span>
                </div>
                <div className="space-y-1">
                  <div className="inline-flex items-center px-2 py-1 rounded-md bg-primary/10 text-primary text-xs font-medium">
                    {data.type?.typeName}
                  </div>
                  <div className="inline-flex items-center px-2 py-1 rounded-md bg-info/10 text-info text-xs font-medium ml-2">
                    {data.category?.categoryName}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePin}
              disabled={isPinning}
              className="p-2"
              title={data.isPinned ? "Unpin announcement" : "Pin announcement"}
            >
              {isPinning ? (
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : (
                <Pin className={`w-4 h-4 ${data.isPinned ? 'fill-current text-warning' : ''}`} />
              )}
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleEdit}
              className="p-2"
              title="Edit announcement"
            >
              <Pencil className="w-4 h-4" />
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              onClick={handleDelete}
              disabled={isDeleting}
              className="p-2 text-destructive border-destructive/20 hover:bg-destructive/10"
              title="Delete announcement"
            >
              {isDeleting ? (
                <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : (
                <Trash2 className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnnouncementCard;