import { useEffect, useState } from "react";
import { toast } from "react-toastify";

interface PremiumStatusResult {
  isPremium: boolean;
  loading: boolean;
  error: string | null;
}

function decodeJWT(token: string): any | null {
  try {
    const payload = token.split(".")[1];
    const decoded = atob(payload);
    return JSON.parse(decoded);
  } catch (err) {
    return null;
  }
}

export const usePremiumStatus = (): PremiumStatusResult => {
  const [isPremium, setIsPremium] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    try {
      const token = localStorage.getItem("token") || "";
      if (!token) toast.error("Token not found");

      const decoded = decodeJWT(token);
      if (!decoded) toast.error("Invalid token");

      setIsPremium(decoded.isPremium === true);
    } catch (err: any) {
      setError(err.message || "Failed to decode token");
      setIsPremium(false);
    } finally {
      setLoading(false);
    }
  }, []);

  return { isPremium, loading, error };
};
