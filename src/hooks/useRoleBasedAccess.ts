import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

const useRoleBasedAccess = (allowedRoles: number[]) => {
  const router = useRouter();
  const { data: session, status } = useSession();

  useEffect(() => {
    if (status === "loading") return;
    if (!session?.user) {
      router.push("/");
      return;
    }

    const { roleId } = session.user;
    if (!allowedRoles.includes(roleId)) {
      router.push("/404");
    }
  }, [session, status, allowedRoles, router]);

  return { session, status };
};

export default useRoleBasedAccess;
