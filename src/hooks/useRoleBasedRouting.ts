"use client";

import { ROUTES, validateRoute } from "@/utils/routeConfig";
import { useSession } from "next-auth/react";
import { usePathname, useRouter } from "next/navigation";
import { useEffect } from "react";

export const useRoleBasedRoute = () => {
  const router = useRouter();
  const pathname = usePathname();
  const { data: session, status } = useSession();

  useEffect(() => {
    if (status === "loading") return;

    const isValid = validateRoute(router, pathname, session?.user || null);

    if (!isValid) {
      // Route validation failed, redirect will be handled by validateRoute
      return;
    }

    // If user is authenticated, redirect to their dashboard
    if (session?.user && pathname === "/") {
      const roleId = session.user.roleId;
      let dashboardPath = "";

      switch (roleId) {
        case 2: // Athlete
          dashboardPath = ROUTES.DASHBOARD.ATHLETE;
          break;
        case 3: // Coach
          dashboardPath = ROUTES.DASHBOARD.COACH;
          break;
        case 4: // Organization
          dashboardPath = ROUTES.DASHBOARD.BUSINESS;
          break;
        default:
          dashboardPath = ROUTES.HOME;
      }

      router.push(dashboardPath);
    }
  }, [router, session, status]);

  return {
    isLoading: status === "loading",
    isAuthenticated: !!session?.user,
    user: session?.user,
  };
};
