import { AthleteProfileTypes } from "@/utils/interfaces";
import { createSlice } from "@reduxjs/toolkit";

const initialState: AthleteProfileTypes = {
  loading: false,
  error: "",
  profileToggle: false,
  isProfileEditable: false,
  isAboutEditable: false,
  socialMediaToggle: true,
  quickLinksToggle: true,
  galleryToggle: true,
  physicalStatsToggle: true,
  height: "",
  openVirtualSession: true,
  athleteSocialMediaList: [
    {
      id: "x",
      icon: "/X.jpeg",
      link: "x.com",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "instagram",
      icon: "/instagram.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "facebook",
      icon: "/facebook.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "youtube",
      icon: "/youtube.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
  ],
  athleteQuickLinksList: [
    {
      id: "quickLink1",
      link: "",
      isEditable: false,
    },
    {
      id: "quickLink2",
      link: "",
      isEditable: false,
    },
    {
      id: "quickLink3",
      link: "",
      isEditable: false,
    },
  ],
  athleteGalleryList: [
    {
      id: "gallery1",
      file: null,
      imageTitle: "First Title",
      isEditable: false,
      url: "",
    },
    {
      id: "gallery2-",
      file: null,
      imageTitle: "Second Title",
      isEditable: true,
      url: "",
    },
  ],
  mySportsList: [
    {
      id: "sport1",
      isPrimary: true,
      sportName: "Cricket",
      sportLevel: "Beginner",
      specilities: [
        { id: "spclty1", specilityName: "Bowler" },
        { id: "spclty2", specilityName: "Keeper" },
        { id: "spclty3", specilityName: "Batsman" },
      ],
      isEditable: false,
    },
    {
      id: "sport2",
      isPrimary: true,
      sportName: "Football",
      sportLevel: "Beginner",
      specilities: [
        { id: "spclty1", specilityName: "General" },
        { id: "spclty2", specilityName: "Defender" },
      ],
      isEditable: false,
    },
    {
      id: "sport3",
      isPrimary: false,
      sportName: "Wallyball",
      sportLevel: "Beginner",
      specilities: [{ id: "spclty1", specilityName: "Bowler" }],
      isEditable: false,
    },
  ],
  sportProfileId: null,
  toggleAchievements: false,
  achievementData: null,
  addedAchievementsList: [],
  selectedState: null,
  selectedLocations: [],
  athleteAddedStateLocationsList: [],
};

const athleteProfileSlice = createSlice({
  name: "athleteProfile",
  initialState,
  reducers: {
    handleUpdateUserInput: (state, action) => {
      const { name, value } = action.payload;
      state[name] = value;
    },
  },
});

export const { handleUpdateUserInput } = athleteProfileSlice.actions;
export default athleteProfileSlice.reducer;
