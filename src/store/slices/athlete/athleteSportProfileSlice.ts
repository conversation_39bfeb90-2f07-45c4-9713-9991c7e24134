import { AthleteSportProfileStates } from "@/utils/interfaces";
import { createSlice } from "@reduxjs/toolkit";

const initialState: AthleteSportProfileStates = {
  loading: false,
  error: "",
  selectedSport: null,
  addedSportSpecilitiesList: [],
  selectedSportLevel: null,
  addedSportLevelsList: [],
  toggleVideoSection: true,
  toggleHighlightLinks: true,
  isEditHighlightLinks: false,
  latestVideoData: {
    title: "",
    aboutVideo: "",
    video: null,
  },
  highlightLinksList: Array(6).fill({
    text: "",
    url: "",
  }),
  addedHighlightVideoList: [],
  toggleMileStone: true,
  isAddMileStone: false,
  mileStoneData: null,
  addedMileStonesList: [],
  toggleVictoryVault: true,
  isVictoryVault: false,
  vicotryVaultData: null,
  addedVictoryVaultList: [],
};

const athleteSportProfileSlice = createSlice({
  name: "athleteSportProfile",
  initialState,
  reducers: {
    handleUpdateUserInput: (state, action) => {
      const { name, value } = action.payload;
      state[name] = value;
    },
  },
});

export const { handleUpdateUserInput } = athleteSportProfileSlice.actions;
export default athleteSportProfileSlice.reducer;
