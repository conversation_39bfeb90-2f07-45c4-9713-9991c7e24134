import { EachSearchItem } from "@/utils/interfaces";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";

interface OrgProfileState {
  loading: boolean;
  error: string | null;
  orgProfile: any[];
  allStates: any[];
  quickLinksToggle: boolean;
  orgQuickLinksList: { id: string; link: string; isEditable: boolean }[];
  orgSocialMediaList: {
    id: string;
    icon: string;
    link: string;
    isEditable: boolean;
    isEnable: boolean;
  }[];
  selectedState: EachSearchItem | null;
  selectedLocations: EachSearchItem[]
  selectedSport: EachSearchItem | null;
  selectedSpecialities: EachSearchItem[];
}

const initialState: OrgProfileState = {
  loading: false,
  error: null,
  orgProfile: [],
  allStates: [],
  quickLinksToggle: true,
  orgQuickLinksList: [
    { id: "quickLink1", link: "", isEditable: false },
    { id: "quickLink2", link: "", isEditable: false },
    { id: "quickLink3", link: "", isEditable: false },
  ],
  orgSocialMediaList: [
    {
      id: "x",
      icon: "/X.jpeg",
      link: "x.com",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "instagram",
      icon: "/instagram.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "facebook",
      icon: "/facebook.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "youtube",
      icon: "/youtube.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
  ],
  selectedState: null,
  selectedLocations: [],
  selectedSport: null,
  selectedSpecialities: [],
};

export const fetchOrgProfile = createAsyncThunk(
  "org/Profile",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const token = JSON.parse(localStorage.getItem("token") || '""');
      const profileDataString = sessionStorage.getItem("profileData");
      const profileData = profileDataString
        ? JSON.parse(profileDataString)
        : null;
      const profileId = profileData?.id;

      const url = `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/allOrganization?id=${profileId}`;
      if (!url) {
        throw new Error("User profile API URL is not defined.");
      }

      const response = await axios.get(url, {
        headers: {
          Authorization: token,
          "Content-Type": "application/json",
        },
      });

      if (response?.data?.status === 200) {
        return fulfillWithValue(response.data.data);
      } else {
        return rejectWithValue(
          response?.data?.message || "Failed to fetch org profile"
        );
      }
    } catch (error) {
      if (error instanceof Error) {
        console.error("fetchOrgProfile error:", error.message);
        return rejectWithValue(error.message);
      }
      console.error("fetchOrgProfile unknown error:", error);
      return rejectWithValue("An unknown error occurred");
    }
  }
);

export const fetchAllStates = createAsyncThunk(
  "org/getAllStates",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const rawToken = localStorage.getItem("token");
      if (!rawToken) return rejectWithValue("Authorization token missing");

      const url = process.env.NEXT_PUBLIC_USER_MANAGEMENT_GET_ALL_STATES;
      if (!url) throw new Error("All States API URL is not defined.");

      console.log("Fetching all states from:", url);

      const response = await axios.get(url);

      if (response.data?.status === 200) {
        return fulfillWithValue(response.data.data);
      } else {
        return rejectWithValue(
          response.data?.message || "Failed to fetch all states"
        );
      }
    } catch (error: any) {
      if (error.response) {
        console.error("API error response:", error.response.data);
        return rejectWithValue(
          error.response.data.message || "API error occurred"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return rejectWithValue("No response from API");
      } else {
        console.error("Error:", error.message);
        return rejectWithValue(error.message || "Unknown error");
      }
    }
  }
);

// Update organization profile API call
export const updateOrgProfile = createAsyncThunk(
  "org/updateProfile",
  async (profileData: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const token = JSON.parse(localStorage.getItem("token") || '""');
      const baseUrl = process.env.NEXT_PUBLIC_USERPROFILE_API_URL;

      if (!baseUrl) {
        throw new Error("User profile API URL is not defined in environment variables.");
      }

      // Use the same endpoint pattern as fetchOrgProfile but with PUT/POST for update
      const url = `${baseUrl}/updateOrganization`;

      console.log("Updating organization profile...");
      console.log("Update API URL:", url);
      console.log("Profile data:", profileData);

      const response = await axios.put(url, profileData, {
        headers: {
          Authorization: token,
          "Content-Type": "application/json",
        },
      });

      console.log("Update API response:", response.data);

      if (response?.data?.status === 200 || response?.data?.success === true) {
        return fulfillWithValue(response.data);
      } else {
        return rejectWithValue(response?.data?.message || "Failed to update organization profile");
      }
    } catch (error) {
      if (error instanceof Error) {
        console.error("Error updating organization profile:", error.message);
        return rejectWithValue(error.message);
      } else {
        console.error("Unknown error:", error);
        return rejectWithValue("An unknown error occurred while updating profile");
      }
    }
  }
);

export const OrgProfileSlice = createSlice({
  name: "orgProfile",
  initialState,
  reducers: {
    updateSelectedState: (state, action) => {
      state.selectedState = action.payload;
    },
    updateSelectedLocations: (state, action) => {
      state.selectedLocations = action.payload;
    },
    updateSelectedSport: (state, action) => {
      state.selectedSport = action.payload;
    },
    updateSelectedSpecialities: (state, action) => {
      state.selectedSpecialities = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchOrgProfile cases
      .addCase(fetchOrgProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchOrgProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.orgProfile = action.payload;
      })
      .addCase(fetchOrgProfile.rejected, (state, action) => {
        state.loading = false;
        state.error =
          (action.payload as string) || "Failed to fetch org profile";
      })

      // updateOrgProfile cases
      .addCase(updateOrgProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateOrgProfile.fulfilled, (state, action) => {
        state.loading = false;
        // Optionally update the profile data in state
      })
      .addCase(updateOrgProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = (action.payload as string) || "Failed to update org profile";
      })

      // fetchAllStates cases
      .addCase(fetchAllStates.pending, (state) => {
        console.log("fetchAllStates payload in pending:");
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllStates.fulfilled, (state, action) => {
        console.log("fetchAllStates payload in reducer:", action.payload);
        state.loading = false;
        state.allStates = action.payload;
      })
      .addCase(fetchAllStates.rejected, (state, action) => {
        console.log("fetchAllStates payload in rejected:");
        state.loading = false;
        state.error =
          (action.payload as string) || "Failed to fetch all states";
      });
  },
});

export const { updateSelectedState, updateSelectedLocations, updateSelectedSport, updateSelectedSpecialities } = OrgProfileSlice.actions;
export default OrgProfileSlice.reducer;
