
import { EachGalleryItem, HomeBanners } from "@/utils/interfaces";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";

const initialState: HomeBanners = {
  loading: false,
  error: "",
  homeBannerImages: [],
};

const apiUrl = "https://api.engageathlete.com/api/content/v1";

export const fetchHomeBanners = createAsyncThunk(
  "CMS/fetchHomeBanners",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axios.get(`${apiUrl}/getpage/1`);
      return fulfillWithValue(response.data.data);
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const homeCMSSlice = createSlice({
  name: "homeCMS",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchHomeBanners.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchHomeBanners.fulfilled, (state, action) => {
        state.loading = false;
        const homeBanners = action.payload?.cmsgalleries?.filter(
          (each: EachGalleryItem) => each?.fileType === "Banner Image"
        );
        state.homeBannerImages = homeBanners;
      })
      .addCase(fetchHomeBanners.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {} = homeCMSSlice.actions;
export default homeCMSSlice.reducer;
