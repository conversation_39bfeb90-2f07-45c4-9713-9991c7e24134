import { CoachProfileTypes } from "@/utils/interfaces";
import { createSlice } from "@reduxjs/toolkit";

const initialState: CoachProfileTypes = {
  loading: false,
  error: "",
  profileToggle: false,
  isProfileEditable: false,
  selectedAgeGroups: [],
  selectedGender: [],
  toggleCoachingFocus: true,
  allCoachingFocusesList: [
    { value: 1, label: "General" },
    { value: 2, label: "Fitness" },
    { value: 3, label: "Strength" },
  ],
  selectedFocuses: [],
  toggleCoachingBackground: true,
  allCoachingBackground: [],
  selectedBackgrounds: [],
  toggleAboutVideo: true,
  aboutVideoFile: null,
  isBioEditable: false,
  toggleShortBio: true,
  shortBio: "",
  toggleSocialMedia: true,
  coachSocialMediaList: [
    {
      id: "x",
      icon: "/X.jpeg",
      link: "x.com",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "instagram",
      icon: "/instagram.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "facebook",
      icon: "/facebook.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "youtube",
      icon: "/youtube.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
    {
      id: "linkedin",
      icon: "/linkedin.svg",
      link: "",
      isEditable: false,
      isEnable: true,
    },
  ],
  toggleQuickLinks: true,
  coachQuickLinksList: [
    {
      id: "quickLink1",
      link: "",
      isEditable: false,
    },
    {
      id: "quickLink2",
      link: "",
      isEditable: false,
    },
    {
      id: "quickLink3",
      link: "",
      isEditable: false,
    },
  ],
  toggleWebsite: true,
  website: "",
  toggleAffiliation: true,
  allAffiliationTypesList: [
    { value: 1, label: "General" },
    { value: 2, label: "School" },
  ],
  selectedAffiliationType: null,
  allCurrentAffiliations: [{ value: 1, label: "General" }],
  selectedCurrentAffiliation: null,
  currentAffiliation: "",
  allWhoAmIRightNowList: [
    { value: 1, label: "General" },
    { value: 2, label: "Other" },
  ],
  selectedWhoAmIRightNow: null,
  otherProfession: "",
  selectedIState: null,
  selectedILocations: [],
  toggleWhyIAm: true,
  allWhyIAmOptions: [
    { value: 1, label: "Give Back" },
    { value: 2, label: "Mentor Youth" },
    { value: 3, label: "Support Beyond Sports" },
  ],
  selectedWhyIAm: [],
  toggleMyCoachingFocus: true,
  allMyCoachingFocusList: [
    { value: 1, label: "Offering-private-training" },
    { value: 2, label: "Cpromoting-camps-clinics" },
    { value: 3, label: "Recruiting Athletes" },
    { value: 4, label: "Mentoring-youth" },
  ],
  selectedMyCoachingFocuses: [],
  toggleWhatIOfferAsCoach: true,
  allCoachOfferList: [
    { value: 1, label: "College-recruiting-preparation" },
    { value: 2, label: "Individual-skill-development" },
    { value: 3, label: "Tryout-preparation" },
    { value: 4, label: "Positive-mindset-coaching" },
  ],
  selectedCoachOffer: [],
  toggleSportInfoSection: true,
  coachSelectedSportsList: [
    {
      id: "sport1",
      isPrimary: true,
      sportName: "Cricket",
      specilities: [
        { id: "spclty1", specilityName: "Bowler" },
        { id: "spclty2", specilityName: "Keeper" },
        { id: "spclty3", specilityName: "Batsman" },
      ],
      isEditable: false,
    },
    {
      id: "sport2",
      isPrimary: true,
      sportName: "Football",
      specilities: [
        { id: "spclty1", specilityName: "General" },
        { id: "spclty2", specilityName: "Defender" },
      ],
      isEditable: false,
    },
    {
      id: "sport3",
      isPrimary: false,
      sportName: "Wallyball",
      specilities: [{ id: "spclty1", specilityName: "Bowler" }],
      isEditable: false,
    },
  ],
  openVirtualSession: true,
  selectedState: null,
  selectedLocations: [],
  coachAddedStateLocationsList: [],
  toggleHighLightVideo: true,
  highLightVideoData: null,
  toggleGallery: true,
  galleryData: null,
  galleriesList: [
    {
      id: 1,
      image: null,
      title: "First Title",
      isEditable: false,
    },
    {
      id: 2,
      image: null,
      title: "Second Title",
      isEditable: true,
    },
  ],
  toggleAvailability: true,
  allTimeZoneList: [
    { value: 1, label: "EST" },
    { value: 2, label: "IST" },
  ],
  availableTimeZone: null,
  generalAvailabilityList: [
    {
      id: "monday",
      day: "Monday",
      isAvailable: false,
      startTime: "",
      endTime: "",
    },
    {
      id: "tuesday",
      day: "Tuesday",
      isAvailable: false,
      startTime: "",
      endTime: "",
    },
    {
      id: "wednesday",
      day: "Wednesday",
      isAvailable: false,
      startTime: "",
      endTime: "",
    },
    {
      id: "thursday",
      day: "Thursday",
      isAvailable: false,
      startTime: "",
      endTime: "",
    },
    {
      id: "friday",
      day: "Friday",
      isAvailable: false,
      startTime: "",
      endTime: "",
    },
    {
      id: "saturday",
      day: "Saturday",
      isAvailable: false,
      startTime: "",
      endTime: "",
    },
    {
      id: "sunday",
      day: "Sunday",
      isAvailable: false,
      startTime: "",
      endTime: "",
    },
  ],
  toBookTime: undefined,
  toggleToBookTime: true,
  availabilityNote: "",
  toggleAvailabilityNote: true,
  toggleCertification: true,
  certificatesData: null,
  isAddCertificates: false,
  coachAddedCertificatesList: [],
  isEditCertificate: false,
  toggleResumeSection: true,
  coachResumeData: null,
  addedResumeData: null,
  toggleContactInfo: true,
  coachContactInfo: {
    firstName: "",
    lastName: "",
    phone: "",
    email: "",
    lastTAndCAccepted: "",
  },
  isEditContactInfo: false,
  togglePhone: true,
  toggleEmail: true,
  govtIdData: {
    title: "",
    documentType: undefined,
    otherType: "",
    expirationDate: undefined,
    file: null,
  },
  addedGovtIdData: null,
  additionalDocList: [
    {
      title: "",
      documentType: undefined,
      otherType: "",
      expirationDate: undefined,
      file: null,
    },
    {
      title: "",
      documentType: undefined,
      otherType: "",
      expirationDate: undefined,
      file: null,
    },
    {
      title: "",
      documentType: undefined,
      otherType: "",
      expirationDate: undefined,
      file: null,
    },
  ],
  declarations: {
    accuracy: false,
    responsibility: false,
    ongoing: false,
    consent: false,
    agreeAll: false,
    eSign: "",
    date: undefined,
  },
};

const coachProfileSlice = createSlice({
  name: "coachProfile",
  initialState,
  reducers: {
    handleCoachInputChange: (state, action) => {
      const { name, value } = action.payload;
      state[name] = value;
    },
  },
});

export const { handleCoachInputChange } = coachProfileSlice.actions;
export default coachProfileSlice.reducer;
