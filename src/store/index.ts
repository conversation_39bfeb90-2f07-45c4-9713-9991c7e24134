import { configureStore } from "@reduxjs/toolkit";
import athleteProfileSlice from "./slices/athlete/athleteProfileSlice";
import athleteSportProfileSlice from "./slices/athlete/athleteSportProfileSlice";
import loginSlice from "./slices/auth/loginSlice";
import coachProfileSlice from "./slices/coach/coachProfileSlice";
import coachSportSlice from "./slices/coach/coachSportSlice";
import commonSlice from "./slices/commonSlice";
import homeCMSSlice from "./slices/homeCMSSlice";
import announcementDashboard from './slices/org-announcement/announcement'
import orgProfile from "./slices/org-announcement/sportsBusiness";


export const store = configureStore({
  reducer: {
    login: loginSlice,
    homeCMS: homeCMSSlice,
    commonSlice: commonSlice,
    athleteProfile: athleteProfileSlice,
    athleteSportProfile: athleteSportProfileSlice,
    announcementDashboard: announcementDashboard,
    coachProfile: coachProfileSlice,
    coachSport: coachSportSlice,
    orgProfile: orgProfile
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
