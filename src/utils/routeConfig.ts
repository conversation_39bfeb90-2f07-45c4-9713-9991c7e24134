import { Session } from "next-auth";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";

export interface RouteConfig {
  path: string;
  roles: number[];
  requiresAuth: boolean;
  validateSlug?: boolean;
}

// Role IDs mapping
export const ROLES = {
  ATHLETE: 2,
  COACH: 3,
  BUSINESS: 4,
};

// Role names mapping
export const ROLE_NAMES = {
  [ROLES.ATHLETE]: "athlete",
  [ROLES.COACH]: "coach",
  [ROLES.BUSINESS]: "business",
};

// Route configurations
export const ROUTES = {
  HOME: "/",
  DASHBOARD: {
    ATHLETE: "/athlete-dashboard",
    COACH: "/coach-dashboard",
    BUSINESS: "/business-dashboard",
  },
  PROFILE: {
    ATHLETE: "/athlete/[slug]",
    COACH: "/coach/[slug]",
    BUSINESS: "/business/[slug]",
  },
  ANNOUNCEMENTS: {
    BUSINESS: "/business/[slug]/announcements",
  },
  SEARCH: {
    ATHLETE: "/athlete-dashboard/search-coach",
    COACH: "/coach-dashboard/search-athlete",
    BUSINESS: "/business-dashboard/search-athlete",
  },
  FAVORITES: {
    ATHLETE: {
      COACH: "/athlete-dashboard/favorites-coach",
      CLUB: "/athlete-dashboard/favorites-club",
    },
    COACH: "/coach-dashboard/favorites-athlete",
    BUSINESS: "/business-dashboard/favorites-athlete",
  },
  PREMIUM: {
    ATHLETE: "/athlete-dashboard/premium-access",
    BUSINESS: "/business-dashboard/premium-access",
  },
};

// Route configurations with role-based access
export const ROUTE_CONFIGS: { [key: string]: RouteConfig } = {
  [ROUTES.HOME]: {
    path: ROUTES.HOME,
    roles: [ROLES.ATHLETE, ROLES.COACH, ROLES.BUSINESS],
    requiresAuth: false,
  },
  [ROUTES.DASHBOARD.ATHLETE]: {
    path: ROUTES.DASHBOARD.ATHLETE,
    roles: [ROLES.ATHLETE],
    requiresAuth: true,
  },
  [ROUTES.DASHBOARD.COACH]: {
    path: ROUTES.DASHBOARD.COACH,
    roles: [ROLES.COACH],
    requiresAuth: true,
  },
  [ROUTES.DASHBOARD.BUSINESS]: {
    path: ROUTES.DASHBOARD.BUSINESS,
    roles: [ROLES.BUSINESS],
    requiresAuth: true,
  },
};

// Helper function to generate profile URL
export const generateProfileUrl = (user: Session["user"]): string => {
  const roleName = ROLE_NAMES[user.roleId];
  const firstName = user?.userFirstName?.toLowerCase() || "";
  const lastName = user?.userLastName?.toLowerCase() || "";
  const userId = user.id;

  return `/${roleName}/${userId}-${firstName}-${lastName}`;
};

// Helper function to parse profile URL
export const parseProfileUrl = (
  url: string
): { roleId: number; userId: string } | null => {
  const parts = url.split("/");
  if (parts.length !== 3) return null;

  const roleName = parts[1];
  const roleId = Object.entries(ROLE_NAMES).find(
    ([_, name]) => name === roleName
  )?.[0];

  if (!roleId) return null;

  const slugParts = parts[2].split("-");
  const userId = slugParts[slugParts.length - 1];

  return {
    roleId: parseInt(roleId),
    userId,
  };
};

// Route validation function
export const validateRoute = (
  router: AppRouterInstance,
  pathname: string,
  user: Session["user"] | null
): boolean => {
  const path = pathname;
  const config = ROUTE_CONFIGS[path];

  if (!config) return true;

  if (user && !config.roles.includes(user.roleId)) {
    router.push(ROUTES.HOME);
    return false;
  }

  return true;
};
