/**
 * Utility functions for generating business-related routes
 */

export interface BusinessInfo {
  businessName: string;
  userId: string | number;
}

/**
 * Generate a business slug from business name and user ID
 * Format: "BusinessName-UserID"
 */
export const generateBusinessSlug = (businessName: string, userId: string | number): string => {
  // Clean business name (remove special characters, replace spaces with hyphens)
  const cleanBusinessName = businessName
    .replace(/[^a-zA-Z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim();
  
  return `${cleanBusinessName}-${userId}`;
};

/**
 * Parse a business slug to extract business name and user ID
 * Format: "BusinessName-UserID"
 */
export const parseBusinessSlug = (slug: string): BusinessInfo => {
  if (!slug) return { businessName: "", userId: "" };
  
  const lastDashIndex = slug.lastIndexOf('-');
  if (lastDashIndex === -1) return { businessName: slug, userId: "" };
  
  const businessName = slug.substring(0, lastDashIndex);
  const userId = slug.substring(lastDashIndex + 1);
  
  return { businessName, userId };
};

/**
 * Generate business profile URL
 */
export const getBusinessProfileUrl = (businessName: string, userId: string | number): string => {
  const slug = generateBusinessSlug(businessName, userId);
  return `/business/${slug}`;
};

/**
 * Generate business announcements URL
 */
export const getBusinessAnnouncementsUrl = (businessName: string, userId: string | number): string => {
  const slug = generateBusinessSlug(businessName, userId);
  return `/business/${slug}/announcements`;
};

/**
 * Generate business settings URL (for future use)
 */
export const getBusinessSettingsUrl = (businessName: string, userId: string | number): string => {
  const slug = generateBusinessSlug(businessName, userId);
  return `/business/${slug}/settings`;
};

/**
 * Generate add announcement URL
 */
export const getAddAnnouncementUrl = (businessName: string, userId: string | number): string => {
  const slug = generateBusinessSlug(businessName, userId);
  return `/business/${slug}/announcements/add`;
};

/**
 * Business route generator object
 */
export const BusinessRoutes = {
  profile: getBusinessProfileUrl,
  announcements: getBusinessAnnouncementsUrl,
  addAnnouncement: getAddAnnouncementUrl,
  settings: getBusinessSettingsUrl,
  generateSlug: generateBusinessSlug,
  parseSlug: parseBusinessSlug,
};
