import type { Config } from "tailwindcss";

const config: Config = {
    darkMode: ["class"],
    content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
  	extend: {
		keyframes: {
			fadeIn: {
			  '0%': { opacity: '0' },
			  '100%': { opacity: '1' },
			},
			slideUp: {
			  '0%': { transform: 'translateY(20px)', opacity: '0' },
			  '100%': { transform: 'translateY(0)', opacity: '1' },
			},
			scroll: {
			  '0%': { transform: 'translateX(0)' },
			  '100%': { transform: 'translateX(-50%)' },
			},
		  },
		  animation: {
			fadeIn: 'fadeIn 0.5s ease-in-out',
			slideUp: 'slideUp 0.5s ease-in-out',
			scroll: 'scroll 20s linear infinite',
			'scroll-slow': 'scroll 50s linear infinite',
		  },
		fontFamily: {
			sans: ['var(--font-raleway)', 'sans-serif'], 
			poppins: ['Poppins', 'sans-serif'],
		  },
		  fontSize: {
			'2xl': '1.5rem',
			'3xl': '2rem',
			'4xl': '2.65rem', //this one is custom
			'5xl': '3rem',
			'6xl': '3.5rem',
			'7xl': '4rem',
			'8xl': '4.5rem',
			'9xl': '5rem',
		  },
  		colors: {			
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		}
  	}
  },
  plugins: [require("tailwindcss-animate")],
};
export default config;
